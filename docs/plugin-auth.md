# Chrome插件认证系统

## 概述

Chrome插件认证系统允许用户通过Google OAuth在Chrome插件中登录，并获得API Key来调用网站的API接口。

## 系统架构

```
Chrome插件 → Google OAuth → 获取Access Token → /api/auth/plugin-login → 验证Token → 创建用户 → 生成API Key → 返回给插件
```

## 数据库变更

### 1. 添加signin_source字段

```sql
ALTER TABLE users ADD COLUMN signin_source VARCHAR(50) DEFAULT 'web';
```

运行迁移脚本：
```bash
psql -d your_database -f data/migrations/add_signin_source.sql
```

## API接口

### 插件登录接口

**端点**: `POST /api/auth/plugin-login`

**请求格式**:
```typescript
{
  "token": "google_access_token_here",       // 必填：Google Access Token
  "extension_id": "chrome-extension-id",     // 可选：插件ID
  "extension_version": "1.0.0"              // 可选：插件版本
}
```

**成功响应**:
```typescript
{
  "success": true,
  "data": {
    "api_key": "sk-xxxxxxxxxxxxx",
    "user": {
      "uuid": "user-uuid",
      "email": "<EMAIL>",
      "nickname": "用户名",
      "avatar_url": "https://avatar-url"
    }
  }
}
```

**错误响应**:
```typescript
{
  "success": false,
  "error": "错误信息"
}
```

## Token验证机制

系统使用Google的`tokeninfo`端点验证Access Token：
```
https://www.googleapis.com/oauth2/v3/tokeninfo?access_token=THE_TOKEN
```

验证内容包括：
- Token有效性
- 过期时间
- 邮箱权限范围(scope)

## Chrome插件集成步骤

### 1. 在插件中获取Google Token

```javascript
// manifest.json
{
  "permissions": ["identity"],
  "oauth2": {
    "client_id": "your_google_client_id",
    "scopes": ["email", "profile"]
  }
}

// 获取Access Token
chrome.identity.getAuthToken({ interactive: true }, function(token) {
  if (token) {
    loginToWebsite(token);
  }
});
```

### 2. 调用登录API

```javascript
async function loginToWebsite(googleToken) {
  try {
    const response = await fetch('https://your-website.com/api/auth/plugin-login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        token: googleToken,
        extension_id: chrome.runtime.id,
        extension_version: chrome.runtime.getManifest().version
      })
    });
    
    const result = await response.json();
    
    if (result.success) {
      // 保存API Key到Chrome Storage
      chrome.storage.local.set({
        'api_key': result.data.api_key,
        'user_info': result.data.user
      });
      
      console.log('登录成功', result.data);
    } else {
      console.error('登录失败', result.error);
    }
  } catch (error) {
    console.error('请求失败', error);
  }
}
```

### 3. 使用API Key调用其他接口

```javascript
async function callAPI(endpoint, data) {
  // 从Chrome Storage获取API Key
  const storage = await chrome.storage.local.get(['api_key']);
  const apiKey = storage.api_key;
  
  if (!apiKey) {
    throw new Error('用户未登录');
  }
  
  const response = await fetch(`https://your-website.com/api${endpoint}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${apiKey}`
    },
    body: JSON.stringify(data)
  });
  
  return response.json();
}

// 示例：调用生成文本API
callAPI('/demo/gen-text', {
  prompt: '你好，世界！',
  provider: 'openrouter',
  model: 'deepseek/deepseek-r1'
}).then(result => {
  console.log('生成结果:', result);
});
```

## 环境变量配置

确保在`.env`文件中配置了Google OAuth：

```env
NEXT_PUBLIC_AUTH_GOOGLE_ID=your_google_client_id
AUTH_GOOGLE_SECRET=your_google_client_secret
```

## 用户管理

### 插件用户标识

- 新用户注册时，`signin_source`字段会被设置为`chrome_extension`
- 已有网站用户使用插件时，`signin_source`保持为`web`
- 用户可以在网站的API Keys页面看到和管理"Chrome Extension" API Key

### API Key管理

- 每个用户只有一个"Chrome Extension" API Key
- 用户可以在网站上删除这个API Key（插件需要重新登录）
- 插件重复登录时会返回相同的API Key

## 安全考虑

1. **Token验证**：使用Google官方tokeninfo端点验证Access Token
2. **权限检查**：验证Token包含email权限范围
3. **过期检查**：验证Token未过期
4. **输入验证**：验证所有输入参数的格式和长度
5. **错误处理**：不泄露敏感信息的错误处理

## 测试

使用提供的测试文件测试API：

```bash
# 查看测试文件
cat debug/plugin-login-test.http

# 使用VS Code REST Client插件或其他HTTP客户端测试
```

## 故障排除

### 常见错误

1. **"Invalid Google token"**
   - 检查Access Token是否有效且未过期
   - 确认Token包含email权限范围
   - 确认Google OAuth配置正确

2. **"Invalid token format"**
   - 检查Token字段是否为字符串且不为空

3. **"Access token does not have email scope"**
   - 确认Chrome插件请求了email权限范围

4. **"Failed to create or find user"**
   - 检查数据库连接和用户创建逻辑

### 调试

- 查看服务器日志中的详细错误信息
- 使用浏览器开发者工具检查网络请求
- 验证Access Token的内容（在tokeninfo端点测试）

## 维护

定期检查：
- Google Token验证的成功率
- API Key使用情况
- 用户登录失败原因
- 数据库性能（特别是新添加的字段查询） 
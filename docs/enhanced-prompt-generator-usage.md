# 增强提示词生成器（EnhancedPromptGenerator）使用说明

## 概述

`EnhancedPromptGenerator` 是一个通用的提示词生成器组件，专为不同AI模型的提示词优化而设计。它基于配置驱动的架构，支持灵活的文案定制和功能开关。

## 特性

- 🎯 **固定API端点**：统一使用 `/api/generate/enhance-prompt` 接口
- 🌐 **国际化支持**：完整的中英文双语支持
- ⚙️ **功能可配置**：支持模式切换、收藏、复制等功能的开关
- 📱 **响应式设计**：完美适配桌面端和移动端
- 🔐 **用户状态感知**：自动处理登录/未登录状态
- 💫 **用户体验优良**：loading状态、错误处理、成功反馈

## 文件结构

```
components/ui/enhanced-prompt-generator.tsx  # 主组件
lib/enhanced-prompt-configs.ts               # 配置生成器
i18n/messages/zh.json                       # 中文国际化
i18n/messages/en.json                       # 英文国际化
```

## 基本用法

### 1. 导入组件和配置

```typescript
import EnhancedPromptGenerator from "@/components/ui/enhanced-prompt-generator";
import { createVeo3PromptConfig } from "@/lib/enhanced-prompt-configs";
import { getTranslations } from "next-intl/server";
```

### 2. 创建配置

```typescript
const t = await getTranslations("enhanced_prompt_generator");

const config = createVeo3PromptConfig({
  title: t("title"),
  subtitle: t("subtitle"),
  inputLabel: t("inputLabel"),
  inputPlaceholder: t("inputPlaceholder"),
  // ... 其他文案配置
});
```

### 3. 使用组件

```typescript
<EnhancedPromptGenerator 
  config={config} 
  className="my-custom-class"
/>
```

## 配置选项

### EnhancedPromptGeneratorConfig 接口

```typescript
interface EnhancedPromptGeneratorConfig {
  texts: {
    title: string;                    // 组件标题
    subtitle: string;                 // 组件副标题
    inputLabel: string;               // 输入区域标签
    inputPlaceholder: string;         // 输入框占位符
    outputLabel: string;              // 输出区域标签
    outputPlaceholder: string;        // 输出框占位符
    generateButton: string;           // 生成按钮文本
    // ... 更多文案配置
  };
  features?: {
    enableModeSwitch?: boolean;       // 是否启用模式切换 (默认: true)
    enableFavorite?: boolean;         // 是否启用收藏功能 (默认: true)
    enableCopy?: boolean;             // 是否启用复制功能 (默认: true)
  };
  request?: {
    modelType?: string;               // 模型类型 (如: "veo-3")
    modelName?: string;               // 模型名称 (如: "veo-3-alpha")
  };
}
```

## 添加新的提示词生成器类型

### 1. 在配置文件中添加新的配置函数

```typescript
// lib/enhanced-prompt-configs.ts
export const createGeminiPromptConfig = (texts: any): EnhancedPromptGeneratorConfig => {
  return {
    texts: {
      // ... 复制相同的文案映射
    },
    features: {
      enableModeSwitch: true,
      enableFavorite: true,
      enableCopy: true,
    },
    request: {
      modelType: "gemini",
      modelName: "gemini-pro",
    },
  };
};
```

### 2. 添加国际化文案

在 `i18n/messages/zh.json` 和 `i18n/messages/en.json` 中添加新的命名空间：

```json
{
  "gemini_prompt_generator": {
    "title": "Gemini 提示词优化器",
    "subtitle": "为 Google Gemini 优化您的提示词",
    // ... 其他文案
  }
}
```

### 3. 在页面中使用

```typescript
const t = await getTranslations("gemini_prompt_generator");
const geminiConfig = createGeminiPromptConfig({
  title: t("title"),
  subtitle: t("subtitle"),
  // ... 其他配置
});

<EnhancedPromptGenerator config={geminiConfig} />
```

## API 接口

组件会向 `/api/generate/enhance-prompt` 发送 POST 请求，请求体包含：

```typescript
{
  input: string,           // 用户输入
  mode: "basic" | "advanced",  // 模式
  locale: string,          // 语言环境
  model_type?: string,     // 模型类型（来自配置）
  model_name?: string      // 模型名称（来自配置）
}
```

## 样式定制

组件使用 Tailwind CSS 和 Shadcn UI，支持通过 `className` 属性添加自定义样式：

```typescript
<EnhancedPromptGenerator 
  config={config}
  className="my-8 bg-white shadow-lg rounded-lg"
/>
```

## 注意事项

1. **国际化**：确保在主消息文件中正确配置了 `enhanced_prompt_generator` 命名空间
2. **API 兼容性**：后端 API 需要支持新增的 `model_type` 和 `model_name` 参数
3. **用户状态**：组件会自动处理用户登录状态，未登录用户可以使用基础模式
4. **收藏功能**：需要现有的分类和收藏 API 支持

## 示例：VEO-3 提示词生成器

当前项目中的 VEO-3 提示词生成器就是使用这个通用组件的完整示例：

- 页面：`app/[locale]/(default)/veo-3-prompt-generator/page.tsx`
- 配置：`lib/enhanced-prompt-configs.ts` 中的 `createVeo3PromptConfig`
- 国际化：`i18n/messages/*.json` 中的 `enhanced_prompt_generator` 命名空间

通过这种方式，我们可以快速为不同的AI模型创建专门的提示词生成器，保持代码的一致性和可维护性。 
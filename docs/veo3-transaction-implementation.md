# Veo3 事务实现说明

## 概述

本实现采用PostgreSQL RPC函数方案，将扣减积分和创建任务放在一个原子事务中处理，确保数据一致性。

## 实现方案

### 1. 数据库结构变更

#### 添加version字段
```sql
-- 文件：data/migrations/add_version_column.sql
ALTER TABLE user_generate_tasks ADD COLUMN IF NOT EXISTS version INT DEFAULT 1;
```

#### PostgreSQL函数
```sql
-- 文件：data/veo3_transaction_functions.sql
-- 包含以下函数：
-- 1. handle_veo3_task_transaction() - 处理事务
-- 2. handle_task_failure() - 处理失败
-- 注：更新外部任务ID使用直接SQL操作，无需RPC函数
```

### 2. 业务流程

```
1. 数据库事务（原子操作）
   ├── 检查积分是否足够
   ├── 扣减积分
   └── 创建任务（状态=processing）

2. 调用第三方API
   ├── 成功 -> 调用updateTaskExternalId()更新任务外部ID
   └── 失败 -> 更新任务状态为failed，返还积分

3. 返回结果给用户

4. 状态轮询（可选）
   ├── 任务完成 -> 更新任务状态为completed
   ├── 任务失败 -> 调用handleTaskFailure()同时更新状态和返还积分
   └── 任务处理中 -> 返回processing状态

5. 回调通知（兜底机制）
   ├── 任务完成 -> 直接更新数据库任务状态为completed
   └── 任务失败 -> 调用handleTaskFailure()同时更新状态和返还积分
```

### 3. 核心优势

1. **原子性**：扣积分和创建任务在同一事务中
2. **一致性**：避免积分扣除但任务创建失败的情况
3. **可靠性**：失败自动回滚，成功后有补偿机制
4. **性能**：PostgreSQL函数减少网络往返
5. **可观测性**：完整的任务生命周期记录

## 使用方法

### 1. 部署数据库变更

```bash
# 1. 在Supabase SQL Editor中执行
-- 执行 data/migrations/add_version_column.sql

# 2. 在Supabase SQL Editor中执行
-- 执行 data/veo3_transaction_functions.sql
```

### 2. API调用

API接口保持不变，但内部逻辑已优化：

```typescript
// 原有的API调用方式不变
POST /api/generate/veo-3
{
  "imageUrl": "...",
  "prompt": "...",
  "model": "veo3_fast"
}
```

### 3. 错误处理

系统会自动处理以下错误场景：
- 积分不足：直接返回错误，不扣积分
- API调用失败：自动返还积分，更新任务状态
- 系统异常：事务回滚，确保数据一致性
- 状态轮询发现失败：通过RPC函数同时更新状态和返还积分
- 回调通知失败：通过RPC函数同时更新状态和返还积分（兜底保障）

## 监控建议

1. **关键指标**
   - 事务成功率
   - API调用成功率
   - 积分返还成功率
   - 任务状态分布
   - 状态轮询发现失败的处理成功率
   - 回调通知处理成功率

2. **告警规则**
   - 积分返还失败
   - 任务长时间处于processing状态
   - 事务失败率过高
   - 回调通知处理失败
   - RPC函数执行失败

## 故障排查

### 常见问题

1. **积分不足**
   - 错误：`credits_not_enough`
   - 解决：用户需要购买积分

2. **API调用失败**
   - 错误：`API request failed`
   - 解决：检查第三方API服务状态

3. **事务失败**
   - 错误：`transaction_failed`
   - 解决：检查数据库连接和权限

4. **回调处理失败**
   - 错误：`RPC failure handling failed`
   - 解决：检查数据库状态，可能需要手动处理积分返还

### 日志查看

```bash
# 查看API日志
kubectl logs -f deployment/app | grep "veo3-video"

# 查看数据库日志
# 在Supabase Dashboard中查看
```

## 升级说明

从原有实现升级到新实现：

1. 执行数据库迁移
2. 部署新代码
3. 监控系统运行状态
4. 逐步迁移用户请求

## 性能优化

1. 数据库连接池设置适当大小
2. 监控PostgreSQL函数执行时间
3. 定期清理过期的任务记录
4. 考虑添加缓存机制

## RPC vs 直接SQL的选择

### 使用RPC函数的场景：
1. **复杂事务操作**：涉及多个表的原子操作
2. **复杂业务逻辑**：需要条件判断、循环等
3. **性能优化**：需要减少网络往返次数
4. **数据一致性**：需要强一致性保证

### 使用直接SQL的场景：
1. **简单CRUD操作**：单表的增删改查
2. **单一操作**：不涉及复杂逻辑
3. **灵活性要求**：需要动态构建查询条件
4. **维护简单性**：减少数据库函数维护成本

在本实现中：
- **扣积分+创建任务**：使用RPC（复杂事务操作）
- **失败补偿**：使用RPC（多表操作+业务逻辑）
- **更新外部ID**：使用直接SQL（简单单表更新）
- **状态轮询失败处理**：使用RPC（确保积分返还）
- **回调失败处理**：使用RPC（兜底保障，确保积分返还）

## 安全考虑

1. 使用Service Role Key确保数据库权限
2. 验证用户身份和权限
3. 记录所有积分变动日志
4. 定期审计积分数据一致性 
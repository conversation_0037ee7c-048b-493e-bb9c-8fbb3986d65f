:root {
  --background: oklch(0.9761 0 0);
  --foreground: oklch(0.1500 0 0);
  --card: oklch(1.0000 0 0);
  --card-foreground: oklch(0.1500 0 0);
  --popover: oklch(1.0000 0 0);
  --popover-foreground: oklch(0.1500 0 0);
  --primary: oklch(0.6746 0.1414 261.3380);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.7536 0.1626 130.5022);
  --secondary-foreground: oklch(0.1500 0 0);
  --muted: oklch(0.8452 0 0);
  --muted-foreground: oklch(0.4000 0 0);
  --accent: oklch(0.6731 0.1624 144.2083);
  --accent-foreground: oklch(1.0000 0 0);
  --destructive: oklch(0.6520 0.2340 26.6909);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.8975 0 0);
  --input: oklch(1.0000 0 0);
  --ring: oklch(0.6746 0.1414 261.3380);
  --chart-1: oklch(0.6746 0.1414 261.3380);
  --chart-2: oklch(0.7536 0.1626 130.5022);
  --chart-3: oklch(0.6731 0.1624 144.2083);
  --chart-4: oklch(0.8442 0.1722 84.9338);
  --chart-5: oklch(0.6991 0.1570 238.9942);
  --sidebar: oklch(0.9761 0 0);
  --sidebar-foreground: oklch(0.1500 0 0);
  --sidebar-primary: oklch(0.6746 0.1414 261.3380);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.6731 0.1624 144.2083);
  --sidebar-accent-foreground: oklch(1.0000 0 0);
  --sidebar-border: oklch(0.8975 0 0);
  --sidebar-ring: oklch(0.6746 0.1414 261.3380);
  --font-sans: Inter;
  --font-serif: Merriweather;
  --font-mono: Monaco;
  --radius: 0.5rem;
  --shadow-2xs: 0.1rem 0.1rem 0.25rem 0.05rem hsl(0 0% 0% / 0.10);
  --shadow-xs: 0.1rem 0.1rem 0.25rem 0.05rem hsl(0 0% 0% / 0.10);
  --shadow-sm: 0.1rem 0.1rem 0.25rem 0.05rem hsl(0 0% 0% / 0.20), 0.1rem 1px 2px -0.95px hsl(0 0% 0% / 0.20);
  --shadow: 0.1rem 0.1rem 0.25rem 0.05rem hsl(0 0% 0% / 0.20), 0.1rem 1px 2px -0.95px hsl(0 0% 0% / 0.20);
  --shadow-md: 0.1rem 0.1rem 0.25rem 0.05rem hsl(0 0% 0% / 0.20), 0.1rem 2px 4px -0.95px hsl(0 0% 0% / 0.20);
  --shadow-lg: 0.1rem 0.1rem 0.25rem 0.05rem hsl(0 0% 0% / 0.20), 0.1rem 4px 6px -0.95px hsl(0 0% 0% / 0.20);
  --shadow-xl: 0.1rem 0.1rem 0.25rem 0.05rem hsl(0 0% 0% / 0.20), 0.1rem 8px 10px -0.95px hsl(0 0% 0% / 0.20);
  --shadow-2xl: 0.1rem 0.1rem 0.25rem 0.05rem hsl(0 0% 0% / 0.50);
  --tracking-normal: 0.05rem;
}

.dark {
  --background: oklch(0.3211 0 0);
  --foreground: oklch(1.0000 0 0);
  --card: oklch(0.3867 0 0);
  --card-foreground: oklch(1.0000 0 0);
  --popover: oklch(0.3867 0 0);
  --popover-foreground: oklch(1.0000 0 0);
  --primary: oklch(0.6746 0.1414 261.3380);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.7536 0.1626 130.5022);
  --secondary-foreground: oklch(1.0000 0 0);
  --muted: oklch(0.5103 0 0);
  --muted-foreground: oklch(1.0000 0 0);
  --accent: oklch(0.6731 0.1624 144.2083);
  --accent-foreground: oklch(1.0000 0 0);
  --destructive: oklch(0.6520 0.2340 26.6909);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.4495 0 0);
  --input: oklch(0.3867 0 0);
  --ring: oklch(0.6746 0.1414 261.3380);
  --chart-1: oklch(0.6746 0.1414 261.3380);
  --chart-2: oklch(0.7536 0.1626 130.5022);
  --chart-3: oklch(0.6731 0.1624 144.2083);
  --chart-4: oklch(0.8442 0.1722 84.9338);
  --chart-5: oklch(0.6991 0.1570 238.9942);
  --sidebar: oklch(0.3211 0 0);
  --sidebar-foreground: oklch(1.0000 0 0);
  --sidebar-primary: oklch(0.6746 0.1414 261.3380);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.6731 0.1624 144.2083);
  --sidebar-accent-foreground: oklch(1.0000 0 0);
  --sidebar-border: oklch(0.4495 0 0);
  --sidebar-ring: oklch(0.6746 0.1414 261.3380);
  --font-sans: Inter;
  --font-serif: Merriweather;
  --font-mono: Monaco;
  --radius: 0.5rem;
  --shadow-2xs: 0.1rem 0.1rem 0.25rem 0.05rem hsl(0 0% 0% / 0.10);
  --shadow-xs: 0.1rem 0.1rem 0.25rem 0.05rem hsl(0 0% 0% / 0.10);
  --shadow-sm: 0.1rem 0.1rem 0.25rem 0.05rem hsl(0 0% 0% / 0.20), 0.1rem 1px 2px -0.95px hsl(0 0% 0% / 0.20);
  --shadow: 0.1rem 0.1rem 0.25rem 0.05rem hsl(0 0% 0% / 0.20), 0.1rem 1px 2px -0.95px hsl(0 0% 0% / 0.20);
  --shadow-md: 0.1rem 0.1rem 0.25rem 0.05rem hsl(0 0% 0% / 0.20), 0.1rem 2px 4px -0.95px hsl(0 0% 0% / 0.20);
  --shadow-lg: 0.1rem 0.1rem 0.25rem 0.05rem hsl(0 0% 0% / 0.20), 0.1rem 4px 6px -0.95px hsl(0 0% 0% / 0.20);
  --shadow-xl: 0.1rem 0.1rem 0.25rem 0.05rem hsl(0 0% 0% / 0.20), 0.1rem 8px 10px -0.95px hsl(0 0% 0% / 0.20);
  --shadow-2xl: 0.1rem 0.1rem 0.25rem 0.05rem hsl(0 0% 0% / 0.50);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);

  --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
  --tracking-tight: calc(var(--tracking-normal) - 0.025em);
  --tracking-normal: var(--tracking-normal);
  --tracking-wide: calc(var(--tracking-normal) + 0.025em);
  --tracking-wider: calc(var(--tracking-normal) + 0.05em);
  --tracking-widest: calc(var(--tracking-normal) + 0.1em);
}

body {
  letter-spacing: var(--tracking-normal);
}

/* Blog Detail Page Custom Styles */
.blog-gradient-bg {
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 50%, var(--accent) 100%);
  opacity: 0.03;
}

.blog-title-gradient {
  background: linear-gradient(135deg, var(--foreground) 0%, var(--primary) 50%, var(--accent) 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.blog-author-card {
  background: linear-gradient(145deg, var(--card) 0%, var(--muted) 100%);
  backdrop-filter: blur(10px);
  border: 1px solid var(--border);
}

.blog-content-card {
  background: var(--card);
  border: 1px solid var(--border);
  backdrop-filter: blur(10px);
}

.blog-back-to-top {
  background: linear-gradient(135deg, var(--primary) 0%, var(--accent) 100%);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.blog-back-to-top:hover {
  transform: translateY(-2px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

/* Enhanced prose styles for better readability */
.prose-enhanced {
  font-family: var(--font-serif);
  line-height: 1.8;
  color: var(--foreground);
}

.prose-enhanced h1,
.prose-enhanced h2,
.prose-enhanced h3,
.prose-enhanced h4,
.prose-enhanced h5,
.prose-enhanced h6 {
  font-family: var(--font-sans);
  font-weight: 700;
  line-height: 1.2;
  color: var(--foreground);
}

.prose-enhanced p {
  color: var(--foreground);
  margin-bottom: 1rem;
}

.prose-enhanced ul,
.prose-enhanced ol {
  color: var(--foreground);
}

.prose-enhanced li {
  color: var(--foreground);
}

.prose-enhanced strong {
  color: var(--foreground);
  font-weight: 600;
}

.prose-enhanced em {
  color: var(--foreground);
}

.prose-enhanced blockquote {
  position: relative;
  background: var(--muted);
  border-radius: var(--radius);
  padding: 1.5rem;
  margin: 2rem 0;
  color: var(--muted-foreground);
  border-left: 4px solid var(--primary);
}

.prose-enhanced blockquote::before {
  content: '"';
  position: absolute;
  top: -0.5rem;
  left: 1rem;
  font-size: 3rem;
  color: var(--primary);
  font-family: serif;
  line-height: 1;
}

.prose-enhanced code {
  font-size: 0.9em;
  padding: 0.2em 0.4em;
  background: var(--muted);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border);
  color: var(--foreground);
  font-family: var(--font-mono);
}

.prose-enhanced pre {
  position: relative;
  background: var(--muted);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: 1.5rem;
  overflow-x: auto;
}

.prose-enhanced pre code {
  background: transparent;
  border: none;
  padding: 0;
  color: var(--foreground);
}

.prose-enhanced a {
  color: var(--primary);
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;
}

.prose-enhanced a:hover {
  text-decoration: underline;
  opacity: 0.8;
}

.prose-enhanced img {
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  margin: 2rem auto;
  max-width: 100%;
  height: auto;
}

.prose-enhanced table {
  border-radius: var(--radius);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  color: var(--foreground);
}

.prose-enhanced th,
.prose-enhanced td {
  color: var(--foreground);
}

/* Smooth scrolling for headings */
.prose-enhanced h1,
.prose-enhanced h2,
.prose-enhanced h3,
.prose-enhanced h4,
.prose-enhanced h5,
.prose-enhanced h6 {
  scroll-margin-top: 2rem;
}

/* Light mode specific enhancements for blog content */
.prose-enhanced {
  /* Ensure text has good contrast in light mode */
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Light mode improvements for better readability */
.blog-content-card .prose-enhanced {
  background: var(--card);
  color: var(--card-foreground);
}

.blog-content-card .prose-enhanced p,
.blog-content-card .prose-enhanced li,
.blog-content-card .prose-enhanced span,
.blog-content-card .prose-enhanced div {
  color: var(--card-foreground);
}

.blog-content-card .prose-enhanced h1,
.blog-content-card .prose-enhanced h2,
.blog-content-card .prose-enhanced h3,
.blog-content-card .prose-enhanced h4,
.blog-content-card .prose-enhanced h5,
.blog-content-card .prose-enhanced h6 {
  color: var(--card-foreground);
  font-weight: 700;
}

/* Improved blockquote styling for light mode */
.prose-enhanced blockquote {
  background: color-mix(in srgb, var(--muted) 70%, var(--background) 30%);
  border-left: 4px solid var(--primary);
  color: var(--card-foreground);
  font-style: italic;
  position: relative;
}

.prose-enhanced blockquote p {
  color: var(--card-foreground);
}

/* Code block improvements for light mode */
.prose-enhanced code {
  background: color-mix(in srgb, var(--muted) 60%, var(--background) 40%);
  color: var(--card-foreground);
  font-weight: 500;
}

.prose-enhanced pre {
  background: color-mix(in srgb, var(--muted) 50%, var(--background) 50%);
  border: 1px solid var(--border);
}

.prose-enhanced pre code {
  color: var(--card-foreground);
  background: transparent;
}

/* Reading progress indicator */
.reading-progress {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, var(--primary) 0%, var(--accent) 100%);
  transform-origin: left;
  z-index: 1000;
}
import "@/app/globals.css";

import { getMessages, getTranslations } from "next-intl/server";
import { locales } from "@/i18n/locale";

import { AppContextProvider } from "@/contexts/app";
import { Inter as FontSans } from "next/font/google";
import { Metadata } from "next";
import { NextAuthSessionProvider } from "@/auth/session";
import { NextIntlClientProvider } from "next-intl";
import { ThemeProvider } from "@/providers/theme";
import { cn } from "@/lib/utils";

const fontSans = FontSans({
  subsets: ["latin"],
  variable: "--font-sans",
});

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations();

  return {
    title: {
      template: `%s`,
      default: t("metadata.title") || "",
    },
    description: t("metadata.description") || "",
    keywords: t("metadata.keywords") || "",
    openGraph: {
      title: t("metadata.title") || "",
      description: t("metadata.description") || "",
      type: "website",
      locale: locale,
      url: process.env.NEXT_PUBLIC_WEB_URL,
      siteName: "Prompt Ark",
      images: [
        {
          url: `${process.env.NEXT_PUBLIC_WEB_URL}/logo.png`,
          width: 1200,
          height: 630,
          alt: "Prompt Ark - AI Prompt Generator",
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title: t("metadata.title") || "",
      description: t("metadata.description") || "",
      images: [`${process.env.NEXT_PUBLIC_WEB_URL}/logo.png`],
    },
  };
}

export default async function RootLayout({
  children,
  params,
}: Readonly<{
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}>) {
  const { locale } = await params;
  const messages = await getMessages();
  const webUrl = process.env.NEXT_PUBLIC_WEB_URL || "";
  const googleAdsenseCode = process.env.NEXT_PUBLIC_GOOGLE_ADCODE || "";

  return (
    <html lang={locale} suppressHydrationWarning>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        {googleAdsenseCode && (
          <meta name="google-adsense-account" content={googleAdsenseCode} />
        )}
        {/* Google AdSense */}
      {googleAdsenseCode && (
          <script
            async
            src={`https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=${googleAdsenseCode}`}
            crossOrigin="anonymous"
          />
        )}

        {/* Plausible Analytics */}
        <script
          defer
          data-domain="promptark.net"
          src="https://app.pageview.app/js/script.js"
        />

        <link rel="icon" href="/favicon.ico" />

        {/* JSON-LD Structured Data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@graph": [
                {
                  "@type": "WebSite",
                  "@id": `${webUrl}/#website`,
                  "url": webUrl,
                  "name": "Prompt Ark",
                  "description": "Free AI prompt generator for creating optimized prompts for ChatGPT, Claude, Gemini and more AI models",
                  "potentialAction": [
                    {
                      "@type": "SearchAction",
                      "target": {
                        "@type": "EntryPoint",
                        "urlTemplate": `${webUrl}/?q={search_term_string}`
                      },
                      "query-input": "required name=search_term_string"
                    }
                  ]
                },
                {
                  "@type": "Organization",
                  "@id": `${webUrl}/#organization`,
                  "name": "Prompt Ark",
                  "url": webUrl,
                  "logo": {
                    "@type": "ImageObject",
                    "url": `${webUrl}/logo.png`,
                    "width": 512,
                    "height": 512
                  },
                  "contactPoint": {
                    "@type": "ContactPoint",
                    "email": "<EMAIL>",
                    "contactType": "customer service"
                  },
                  "sameAs": []
                },
                {
                  "@type": "SoftwareApplication",
                  "@id": `${webUrl}/#software`,
                  "name": "Prompt Ark AI Prompt Generator",
                  "applicationCategory": "WebApplication",
                  "operatingSystem": "Any",
                  "description": "Free AI prompt generator that creates optimized prompts for ChatGPT, Claude, Gemini, and other AI models using advanced prompt engineering techniques",
                  "offers": {
                    "@type": "Offer",
                    "price": "0",
                    "priceCurrency": "USD",
                    "availability": "https://schema.org/InStock"
                  },
                  "featureList": [
                    "AI Prompt Generation",
                    "Prompt Engineering Tools", 
                    "Multi-Model Support",
                    "Template Library",
                    "Prompt Optimization"
                  ],
                  "screenshot": `${webUrl}/imgs/features/1.png`,
                  "url": webUrl,
                  "publisher": {
                    "@id": `${webUrl}/#organization`
                  }
                }
              ]
            })
          }}
        />

        {locales &&
          locales.map((loc) => (
            <link
              key={loc}
              rel="alternate"
              hrefLang={loc}
              href={`${webUrl}${loc === "en" ? "" : `/${loc}`}/`}
            />
          ))}
        <link rel="alternate" hrefLang="x-default" href={webUrl} />
      </head>
      <body
        className={cn(
          "min-h-screen bg-background font-sans antialiased overflow-x-hidden",
          fontSans.variable
        )}
      >
        <NextIntlClientProvider messages={messages}>
          <NextAuthSessionProvider>
            <AppContextProvider>
              <ThemeProvider attribute="class" disableTransitionOnChange>
                {children}
              </ThemeProvider>
            </AppContextProvider>
          </NextAuthSessionProvider>
        </NextIntlClientProvider>

        <script
          type="text/javascript"
          dangerouslySetInnerHTML={{
            __html: `
              (function(c,l,a,r,i,t,y){
                c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
                t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
                y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
              })(window, document, "clarity", "script", "rvtrwpq292");
            `,
          }}
        />
      </body>
    </html>
  );
}

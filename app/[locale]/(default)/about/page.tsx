import About from "@/components/blocks/about";
import { getAboutPage } from "@/services/page";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const page = await getAboutPage(locale);
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/about`;

  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/about`;
  }

  return {
    title: page.metadata.title,
    description: page.metadata.description,
    keywords: "",
    alternates: {
      canonical: canonicalUrl,
    },
  };
}

export default async function AboutPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const page = await getAboutPage(locale);

  return <About data={page} />;
} 
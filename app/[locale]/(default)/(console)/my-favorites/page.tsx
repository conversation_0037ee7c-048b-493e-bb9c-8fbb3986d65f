import Empty from "@/components/blocks/empty";
import FavoritesSlot from "@/components/console/slots/favorites";
import { getUserUuid } from "@/services/user";
import { getTranslations } from "next-intl/server";
import { getUserFavoritesWithCategories } from "@/services/prompt";

export default async function MyFavoritesPage(props: {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
  const t = await getTranslations();
  const userUuid = await getUserUuid();

  if (!userUuid) {
    return <Empty message="no auth" />;
  }

  const searchParams = await props.searchParams;
  const page = Number(searchParams.page) || 1;
  const limit = Number(searchParams.limit) || 20;
  const categoryUuid = typeof searchParams.category === 'string' ? searchParams.category : undefined;
  const keyword = typeof searchParams.keyword === 'string' ? searchParams.keyword : undefined;

  const data = await getUserFavoritesWithCategories(userUuid, page, limit, categoryUuid, keyword);

  return (
    <FavoritesSlot
      favorites={data.favorites}
      categories={data.categories}
      pagination={data.pagination}
      selectedCategory={categoryUuid}
      searchKeyword={keyword}
    />
  );
} 
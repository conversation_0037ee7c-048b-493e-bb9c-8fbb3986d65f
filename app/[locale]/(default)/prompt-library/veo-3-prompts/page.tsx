import Hero from "@/components/blocks/hero";
import CTA from "@/components/blocks/cta";
import PromptExamples from "@/components/blocks/prompt-examples";
import { getVeo3PromptLibraryPage } from "@/services/page";
import { getTranslations } from "next-intl/server";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/prompt-library/veo-3-prompts`;

  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/prompt-library/veo-3-prompts`;
  }

  const page = await getVeo3PromptLibraryPage(locale);

  return {
    title: page.title,
    description: page.description,
    keywords: "",
    alternates: {
      canonical: canonicalUrl,
    },
  };
}

export default async function Veo3PromptLibraryPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const page = await getVeo3PromptLibraryPage(locale);
  const t = await getTranslations("enhanced_prompt_generator");

  const examplesConfig = {
    copyButton: t("copyButton"),
    successCopied: t("successCopied"),
    errorCopyFailed: t("errorCopyFailed"),
  };

  return (
    <>
      {page.hero && <Hero hero={page.hero} />}
      {page.examples && (
        <PromptExamples 
          section={page.examples}
          config={examplesConfig}
          className="py-16"
        />
      )}
      {page.cta && <CTA section={page.cta} />}
    </>
  );
} 
import Branding from "@/components/blocks/branding";
import CTA from "@/components/blocks/cta";
import FAQ from "@/components/blocks/faq";
import Feature from "@/components/blocks/feature";
import Feature1 from "@/components/blocks/feature1";
import Feature2 from "@/components/blocks/feature2";
import Feature3 from "@/components/blocks/feature3";
import Hero from "@/components/blocks/hero";
import Showcase from "@/components/blocks/showcase";
import Stats from "@/components/blocks/stats";
import Testimonial from "@/components/blocks/testimonial";
import ChatInterface from "@/components/ui/chat-interface";
import { createLyraChatConfig } from "@/lib/chat-configs";
import { getLyraPromptGeneratorPage } from "@/services/page";
import { getTranslations } from "next-intl/server";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/lyra-prompt`;

  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/lyra-prompt`;
  }

  const page = await getLyraPromptGeneratorPage(locale);

  return {
    title: page.title,
    description: page.description,
    keywords: "",
    alternates: {
      canonical: canonicalUrl,
    },
  };
}

export default async function LyraPromptGeneratorPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const page = await getLyraPromptGeneratorPage(locale);
  const t = await getTranslations("lyra_prompt");

  const lyraChatConfig = createLyraChatConfig({
    title: t("title"),
    subtitle: t("subtitle"),
    locale: locale,
  });

  return (
    <>
      {page.hero && <Hero hero={page.hero} />}
      <ChatInterface 
        config={lyraChatConfig} 
        className="py-12"
      />
      {page.branding && <Branding section={page.branding} />}
      {page.usage && <Feature3 section={page.usage} />}
      {page.introduce && <Feature1 section={page.introduce} />}
      {page.benefit && <Feature2 section={page.benefit} />}
      {page.feature && <Feature section={page.feature} />}
      {page.showcase && <Showcase section={page.showcase} />}
      {page.stats && <Stats section={page.stats} />}
      {page.testimonial && <Testimonial section={page.testimonial} />}
      {page.faq && <FAQ section={page.faq} />}
      {page.cta && <CTA section={page.cta} />}
    </>
  );
}

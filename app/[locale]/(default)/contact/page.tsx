import Contact from "@/components/blocks/contact";
import { getContactPage } from "@/services/page";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const page = await getContactPage(locale);
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/contact`;

  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/contact`;
  }

  return {
    title: page.metadata.title,
    description: page.metadata.description,
    keywords: "",
    alternates: {
      canonical: canonicalUrl,
    },
  };
}

export default async function ContactPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const page = await getContactPage(locale);

  return <Contact data={page} />;
} 
import { getTranslations } from "next-intl/server";
import Veo3JsonGenerator from "@/components/ui/veo-3-json-generator";
import Hero from "@/components/blocks/hero";
import { getPage } from "@/services/page";
import CTA from "@/components/blocks/cta";
import FAQ from "@/components/blocks/faq";
import Feature from "@/components/blocks/feature";
import Feature1 from "@/components/blocks/feature1";
import Feature2 from "@/components/blocks/feature2";
import Feature3 from "@/components/blocks/feature3";
import Stats from "@/components/blocks/stats";
import ReactTweetExamples from "@/components/blocks/react-tweet-examples";
import Showcase from "@/components/blocks/showcase";
import Testimonial from "@/components/blocks/testimonial";
import { V3PromptGeneratorPage } from "@/types/pages/veo3-prompt-generator";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/veo-3-json-prompt-generator`;

  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/veo-3-json-prompt-generator`;
  }

  const t = await getTranslations("veo3_json_generator");

  return {
    title: t("title"),
    description: t("description"),
    keywords: "veo-3, json prompt, video generation, ai prompt generator",
    alternates: {
      canonical: canonicalUrl,
    },
  };
}

export default async function Veo3JsonPromptPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const page = (await getPage("veo-3-json-prompt-generator", locale)) as V3PromptGeneratorPage
  const t = await getTranslations("veo3_json_generator");
  const ai_t = await getTranslations("veo3_json_prompt_ai_generator")

  // 根据语言和页面内容生成结构化数据
  const baseUrl = process.env.NEXT_PUBLIC_WEB_URL;
  const pageUrl = locale === "en" 
    ? `${baseUrl}/veo-3-json-prompt-generator`
    : `${baseUrl}/${locale}/veo-3-json-prompt-generator`;

  const structuredData = {
    "@context": "https://schema.org",
    "@graph": [
      {
        "@type": "WebApplication",
        "@id": `${pageUrl}#webapp`,
        "name": page.title,
        "description": page.description,
        "inLanguage": locale === "zh" ? "zh-CN" : "en-US",
        "applicationCategory": "UtilityApplication",
        "operatingSystem": "Any",
        "browserRequirements": "Requires JavaScript",
        "url": pageUrl,
        "author": {
          "@type": "Organization",
          "name": "Prompt Ark Team",
          "url": baseUrl,
          "logo": {
            "@type": "ImageObject",
            "url": `${baseUrl}/logo.png`
          }
        },
        "offers": {
          "@type": "Offer",
          "price": "0",
          "priceCurrency": "USD",
          "availability": "https://schema.org/InStock"
        },
        "featureList": page.feature?.items?.map(item => item.title) || [],
        "applicationSubCategory": locale === "zh" ? "AI视频提示词工具" : "AI Video Prompt Tool"
      },
      {
        "@type": "HowTo",
        "@id": `${pageUrl}#howto`,
        "name": page.usage?.title,
        "description": page.usage?.description,
        "inLanguage": locale === "zh" ? "zh-CN" : "en-US",
        "step": page.usage?.items?.map((item, index) => ({
          "@type": "HowToStep",
          "position": index + 1,
          "name": item.title,
          "text": item.description
        })) || []
      },
      {
        "@type": "FAQPage",
        "@id": `${pageUrl}#faq`,
        "mainEntity": page.faq?.items?.map(item => ({
          "@type": "Question",
          "name": item.title,
          "acceptedAnswer": {
            "@type": "Answer",
            "text": item.description
          }
        })) || []
      },
      {
        "@type": "SoftwareSourceCode",
        "@id": `${pageUrl}#tool`,
        "name": locale === "zh" ? "Veo 3 JSON提示词生成器" : "Veo 3 JSON Prompt Generator",
        "description": locale === "zh" 
          ? "专业的结构化JSON提示词生成工具，支持模板模式和AI生成模式"
          : "Professional structured JSON prompt generation tool with template and AI modes",
        "programmingLanguage": "JSON",
        "runtimePlatform": "Web Browser",
        "targetProduct": {
          "@type": "SoftwareApplication",
          "name": "Google Veo 3"
        }
      }
    ]
  };

  const examplesConfig = {
    copyButton: t("copyButton"),
    successCopied: t("successCopied"),
    errorCopyFailed: t("errorCopyFailed"),
  };

  const config = {
    config: {
      title: t("title"),
      subtitle: t("subtitle"),
      templateMode: t("templateMode"),
      aiMode: t("aiMode"),
      selectTemplate: t("selectTemplate"),
      generateJson: t("generateJson"),
      copyJson: t("copyJson"),
      inputPrompt: t("inputPrompt"),
      generateButton: t("generateButton"),
      generating: t("generating"),
      jsonPreview: t("jsonPreview"),
      sampleVideo: t("sampleVideo"),
      successCopied: t("successCopied"),
      errorCopyFailed: t("errorCopyFailed"),
      errorNoInput: t("errorNoInput"),
      errorGenerateFailed: t("errorGenerateFailed"),
      successGenerated: t("successGenerated")
    },
    aiModeConfig: {
      inputPrompt: ai_t("inputPrompt"),
      generateButton: ai_t("generateButton"),
      generating: ai_t("generating"),
      jsonPreview: ai_t("jsonPreview"),
      copyJson: ai_t("copyJson"),
      successGenerated: ai_t("successGenerated"),
      successCopied: ai_t("successCopied"),
      errorNoInput: ai_t("errorNoInput"),
      errorGenerateFailed: ai_t("errorGenerateFailed"),
      errorCopyFailed: ai_t("errorCopyFailed"),
      advancedGenerating: ai_t("advancedGenerating"),
      advancedGenerateButton: ai_t("advancedGenerateButton"),
      basicModeLabel: ai_t("basicModeLabel"),
      advancedModeLabel: ai_t("advancedModeLabel"),
      basicModeHint: ai_t("basicModeHint"),
      advancedModeHint: ai_t("advancedModeHint"),
      errorNotLoggedIn: ai_t("errorNotLoggedIn"),
      errorInsufficientCredits: ai_t("errorInsufficientCredits"),
      creditsRemaining: ai_t("creditsRemaining"),
      creditsInsufficient: ai_t("creditsInsufficient"),
      buyCredits: ai_t("buyCredits"),
      inputPlaceholder: ai_t("inputPlaceholder"),
      emptyStateText: ai_t("emptyStateText"),
    }
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData)
        }}
      />
      {page.hero && <Hero hero={page.hero} />}
      <section className="py-16 bg-background" id="generator">
        <div className="container mx-auto px-4">
          <div className="max-w-7xl mx-auto">
            <Veo3JsonGenerator config={config.config} aiModeConfig={config.aiModeConfig} />
          </div>
        </div>
      </section>
      {page.usage && <Feature3 section={page.usage} />}
      {page.introduce && <Feature1 section={page.introduce} />}
      {page.benefit && <Feature2 section={page.benefit} />}
      {page.feature && <Feature section={page.feature} />}
      {page.showcase && <Showcase section={page.showcase} />}
      {page.stats && <Stats section={page.stats} />}
      {page.testimonial && <Testimonial section={page.testimonial} />}
      {page.faq && <FAQ section={page.faq} />}
      {page.twitterExamples && (
        <ReactTweetExamples 
          section={page.twitterExamples}
        />
      )}
      {page.cta && <CTA section={page.cta} />}
    </>
  );
}

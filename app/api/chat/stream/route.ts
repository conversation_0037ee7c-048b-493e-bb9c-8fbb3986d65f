import { respErr } from "@/lib/resp";
import { CreditsAmount, CreditsTransType, decreaseCredits, getUserCredits } from "@/services/credit";
import { getUserUuid } from "@/services/user";
import { createOpenRouter } from "@openrouter/ai-sdk-provider";
import { streamText } from "ai";
import { NextRequest } from "next/server";

export async function POST(req: NextRequest) {
  try {
    const { messages, model, provider = "openrouter" } = await req.json();

    console.log("Chat request:", { messages: messages.length, model, provider });

    if (!messages || !model) {
      return new Response("Missing required parameters", { status: 400 });
    }

    const openrouter = createOpenRouter({
      apiKey: process.env.OPENROUTER_API_KEY,
    });

    const user_uuid = await getUserUuid();

    var cost_credit = 1;
    if (model === "deepseek/deepseek-chat-v3-0324:free") {
      cost_credit = 0;
    }

    if (cost_credit > 0) {
      const credits = await getUserCredits(user_uuid);
      if (credits.left_credits < cost_credit) {
        return respErr("credits not enough, please buy credits");
      }

      if (cost_credit > 0) {
        await decreaseCredits({
          user_uuid: user_uuid,
          trans_type: CreditsTransType.Prompt_Generator,
          credits: CreditsAmount.PromptGeneratorCost,
        });
      }
    }

    const result = await streamText({
      model: openrouter("deepseek/deepseek-chat-v3-0324:free"),
      messages: messages
    });

    console.log("Returning stream response");
    return result.toDataStreamResponse();
  } catch (error) {
    console.error("Chat stream error:", error);
    return new Response("Internal server error", { status: 500 });
  }
}

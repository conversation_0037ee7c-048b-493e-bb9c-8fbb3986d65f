import { respData, respErr } from "@/lib/resp";
import { getIsoTimestr } from "@/lib/time";
import { findUserGenerateTaskByTaskId, updateUserGenerateTaskByTaskId, updateTaskResultIdempotent } from "@/models/user-generate-task";
import { CreditsAmount, CreditsTransType, decreaseCredits } from "@/services/credit";
import { getUserUuid } from "@/services/user";
import { TaskStatus, TaskType } from "@/types/user-generate-task";
import { handleTaskFailure } from "@/models/db";

export async function GET(req: Request) {
    try {
        const { searchParams } = new URL(req.url);
        const task_id = searchParams.get('task_id');

        if (!task_id) {
            return respErr("task_id parameter is required");
        }

        // 获取当前用户UUID
        const user_uuid = await getUserUuid();
        if (!user_uuid) {
            return respErr("unauthorized");
        }

        const task = await findUserGenerateTaskByTaskId(task_id);
        if (!task) {
            console.error("Task not found:", task_id);
            return respErr("task not found");
        }

        // 验证任务是否属于当前用户
        if (task.user_uuid !== user_uuid) {
            console.error("User attempting to access task that doesn't belong to them:", {
                current_user: user_uuid,
                task_owner: task.user_uuid,
                task_id: task_id
            });
            return respErr("unauthorized");
        }

        if (task.task_status === TaskStatus.COMPLETED) {
            return respData({
                message: "success",
                data: {
                    task_status: task.task_status,
                    resultUrls: task.task_result,
                }
            })
        } else if (task.task_status === TaskStatus.FAILED) {
            return respData({
                message: "success",
                data: {
                    task_status: task.task_status,
                    resultUrls: [],
                }
            })
        }

        const api_key = process.env.KIE_API_KEY;

        const response = await fetch(`https://kieai.erweima.ai/api/v1/veo/record-info?taskId=${task_id}`, {
            method: "GET",
            headers: {
                "Authorization": `Bearer ${api_key}`
            }
        });
        if (!response.ok) {
            return respErr(`get veo3 task status failed: ${response.status} ${response.statusText}`);
        }

        const result = await response.json();
        console.log("API response:", result);

        if (result.code === 200) {
            if (result.data.successFlag === 1) {
                // 使用幂等性更新函数
                const updateResult = await updateTaskResultIdempotent(
                    task_id,
                    user_uuid,
                    TaskStatus.COMPLETED,
                    result.data.response.resultUrls[0]
                );
                
                if (updateResult.success) {
                    console.log(`Task ${task_id} updated to COMPLETED via status polling`);
                    return respData({
                        message: "success",
                        data: {
                            task_status: TaskStatus.COMPLETED,
                            resultUrls: result.data.response.resultUrls,
                        }
                    });
                } else {
                    // 如果更新失败，可能是已经被其他请求更新了，返回当前状态
                    const latestTask = await findUserGenerateTaskByTaskId(task_id);
                    return respData({
                        message: "success",
                        data: {
                            task_status: latestTask?.task_status || TaskStatus.PROCESSING,
                            resultUrls: latestTask?.task_status === TaskStatus.COMPLETED ? 
                                (latestTask.task_result ? [latestTask.task_result] : []) : [],
                        }
                    });
                }
            } else if (result.data.successFlag === 2) {
                // 任务失败，使用RPC函数同时更新状态和返还积分
                const refundTransType = task.task_type === TaskType.VEO3_FAST ? 
                    CreditsTransType.Veo3FastGeneratorRefund : 
                    CreditsTransType.Veo3GeneratorRefund;
                
                const failureResult = await handleTaskFailure({
                    task_id: task.id!,
                    user_uuid: user_uuid,
                    refund_credits: task.task_cost_credit,
                    refund_trans_type: refundTransType,
                    error_info: `API task failed: ${JSON.stringify(result.data)}`
                });
                
                if (failureResult.success) {
                    console.log(`Task ${task_id} updated to FAILED and credits refunded via status polling`);
                } else {
                    console.log(`Task ${task_id} failure handling failed: ${failureResult.message}`);
                    // 如果RPC失败，不处理等待callback处理
                }
                
                return respData({
                    message: "success",
                    data: {
                        task_status: TaskStatus.FAILED,
                        resultUrls: [],
                    }
                });
            }
            return respData({
                message: "success",
                data: {
                    task_status: TaskStatus.PROCESSING,
                    resultUrls: [],
                }
            })
        } else {
            console.error(`get veo3 task status failed: ${result}`);
            return respErr(`get veo3 task status failed, please try again later`);
        }
    } catch (error) {
        console.error(error);
        return respErr("internal server error");
    }
}
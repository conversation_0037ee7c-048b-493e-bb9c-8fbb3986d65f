import { respData, respErr } from "@/lib/resp";
import { generateCallbackToken, updateTaskExternalId } from "@/models/user-generate-task";
import { CreditsAmount, CreditsTransType } from "@/services/credit";
import { getUserUuid } from "@/services/user";
import { TaskType } from "@/types/user-generate-task";
import { executeVeo3TaskTransaction, handleTaskFailure } from "@/models/db";
import { Ms_Madi } from "next/font/google";

export async function POST(req: Request) {
    let taskId: number | null = null;
    let userUuid: string | null = null;
    let costCredit: number = 0;
    let model: string = "";

    try {
        const { imageUrl, prompt, model: requestModel } = await req.json();
        console.log("veo3-video-generator: ", imageUrl, prompt, requestModel);
        
        userUuid = await getUserUuid();
        model = requestModel;

        if (!prompt || !model || !userUuid) {
            return respErr("invalid params");
        }

        // 白名单检查：只允许特定的模型值
        const allowedModels = ["veo3", "veo3_fast"];
        if (!allowedModels.includes(model)) {
            return respErr("invalid model, supported models: " + allowedModels.join(", "));
        }

        costCredit = model === "veo3_fast" ? CreditsAmount.Veo3FastGeneratorCost : CreditsAmount.Veo3GeneratorCost;
        const taskType = model === "veo3_fast" ? TaskType.VEO3_FAST : TaskType.VEO3;
        const transType = model === "veo3_fast" ? CreditsTransType.Veo3FastGenerator : CreditsTransType.Veo3Generator;

        // 生成回调验证token
        const callbackToken = generateCallbackToken(userUuid);

        // 步骤1：数据库事务（扣积分+创建任务）
        const transactionResult = await executeVeo3TaskTransaction({
            user_uuid: userUuid,
            task_type: taskType,
            task_params: {
                imageUrl: imageUrl,
                prompt: prompt,
                model: model,
            },
            task_cost_credit: costCredit,
            callback_token: callbackToken,
            trans_type: transType
        });

        if (!transactionResult.success) {
            console.error("Transaction failed:", transactionResult);
            return respErr(transactionResult.error === 'credits_not_enough' ? 
                "credits not enough, please buy credits" : 
                "Transaction failed, please try again later");
        }

        taskId = transactionResult.task_id!;
        console.log("Transaction success, task_id:", taskId);

        // 步骤2：调用第三方API
        const apiResult = await callExternalAPI({
            prompt,
            imageUrl,
            model,
            callbackToken
        });

        if (apiResult.success && apiResult.data?.taskId) {
            // 步骤3：更新任务的外部ID
            const updateSuccess = await updateTaskExternalId(
                taskId,
                userUuid,
                apiResult.data.taskId
            );

            if (!updateSuccess) {
                console.error("Failed to update external task ID");
                // 这里不需要失败处理，因为任务已经创建成功，只是外部ID更新失败
            }

            return respData({
                message: "success",
                data: apiResult.data,
                internal_task_id: taskId
            });
        } else {
            // API失败，进入补偿流程
            console.error("API call failed:", apiResult.error);
            await handleTaskFailureWithRetry(taskId, userUuid, costCredit, model, apiResult.error);
            return respErr("API request failed, please try again later or contact support");
        }
        
    } catch (error) {
        console.error("veo3-video error:", error);
        
        // 如果任务已创建但后续失败，进入补偿流程
        if (taskId && userUuid) {
            await handleTaskFailureWithRetry(taskId, userUuid, costCredit, model, error);
        }
        
        return respErr("internal server error");
    }
}

// 调用第三方API的函数
async function callExternalAPI(params: {
    prompt: string;
    imageUrl: string;
    model: string;
    callbackToken: string;
}) {
    try {
        const api_key = process.env.KIE_API_KEY;
        const imageUrls = params.imageUrl.length > 0 ? [params.imageUrl] : [];
        const baseUrl = process.env.NEXTAUTH_URL || "http://localhost:3000";
        const callbackUrl = `${baseUrl}/api/generate/veo-3/callback?token=${params.callbackToken}`;
        
        const headers = new Headers();
        headers.append("Content-Type", "application/json");
        headers.append("Accept", "application/json");
        headers.append("Authorization", `Bearer ${api_key}`);

        const raw = JSON.stringify({
            "prompt": params.prompt,
            "imageUrls": imageUrls,
            "model": params.model,
            "watermark": "",
            "callBackUrl": callbackUrl
        });

        const requestOptions = {
            method: "POST",
            headers: headers,
            body: raw,
            redirect: "follow" as RequestRedirect
        };

        const response = await fetch("https://kieai.erweima.ai/api/v1/veo/generate", requestOptions);

        if (!response.ok) {
            const errorText = await response.text();
            return { 
                success: false, 
                error: `HTTP ${response.status}: ${errorText}` 
            };
        }

        const result = await response.json();
        // const result = {
        //     code: 200,
        //     message: "success",
        //     data: {
        //         taskId: "53088af63a7e140dde17334cff49544c"
        //     }
        // }
        console.log("API response:", result);
        
        if (result.code === 200) {
            return { 
                success: true, 
                data: result.data 
            };
        } else {
            return { 
                success: false, 
                error: `API error: ${result.message || 'Unknown error'}` 
            };
        }
    } catch (error) {
        console.error("API call exception:", error);
        return { 
            success: false, 
            error: error instanceof Error ? error.message : 'Unknown error' 
        };
    }
}

// 处理任务失败的函数（带重试）
async function handleTaskFailureWithRetry(
    taskId: number,
    userUuid: string,
    costCredit: number,
    model: string,
    error: any,
    maxRetries: number = 3
) {
    const refundTransType = model === "veo3_fast" ? 
        CreditsTransType.Veo3FastGeneratorRefund : 
        CreditsTransType.Veo3GeneratorRefund;

    const errorInfo = error instanceof Error ? error.message : 
        (typeof error === 'string' ? error : JSON.stringify(error));

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            const result = await handleTaskFailure({
                task_id: taskId,
                user_uuid: userUuid,
                refund_credits: costCredit,
                refund_trans_type: refundTransType,
                error_info: errorInfo
            });

            if (result.success) {
                console.log(`Task failure handled successfully on attempt ${attempt}`);
                return;
            } else {
                console.error(`Task failure handling failed on attempt ${attempt}:`, result);
                if (attempt === maxRetries) {
                    // 最后一次尝试失败，记录错误
                    console.error("All attempts to handle task failure failed. Manual intervention required.");
                    // 这里可以添加告警或记录到特殊表中
                }
            }
        } catch (error) {
            console.error(`Task failure handling exception on attempt ${attempt}:`, error);
            if (attempt === maxRetries) {
                console.error("All attempts to handle task failure failed with exceptions. Manual intervention required.");
            }
        }

        // 如果不是最后一次尝试，等待一段时间后重试
        if (attempt < maxRetries) {
            await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
        }
    }
}
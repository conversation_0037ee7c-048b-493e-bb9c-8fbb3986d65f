import { respData, respErr, respOk } from "@/lib/resp";
import { 
    findUserGenerateTaskByTaskId, 
    findUserGenerateTaskByToken,
    validateCallbackToken,
    updateUserGenerateTaskByTaskId,
    updateTaskStatusIdempotent} from "@/models/user-generate-task";
import { TaskStatus, TaskType } from "@/types/user-generate-task";
import { CreditsAmount, CreditsTransType, increaseCredits } from "@/services/credit";
import { getIsoTimestr } from "@/lib/time";
import { handleTaskFailure } from "@/models/db";

export async function POST(req: Request) {
    try {
        // 验证回调token
        const { searchParams } = new URL(req.url);
        const callbackToken = searchParams.get('token');
        
        if (!callbackToken) {
            console.error("Missing callback token");
            return respErr("unauthorized");
        }

        // 根据token查找任务
        const task = await findUserGenerateTaskByToken(callbackToken);
        if (!task) {
            console.error("Task not found for token:", callbackToken);
            return respErr("unauthorized");
        }

        // 验证token是否属于该用户
        if (!validateCallbackToken(callbackToken, task.user_uuid)) {
            console.error("Invalid callback token for user:", task.user_uuid);
            return respErr("unauthorized");
        }

        const body = await req.json();
        console.log("Veo3 callback received:", body);
        
        const { code, msg, data } = body;
        
        // 从新的数据结构中提取必要信息
        const task_id = data?.taskId;

        if (!task_id) {
            console.error("Missing task_id in callback");
            return respErr("missing task_id");
        }

        // 验证task_id是否匹配
        if (task.task_id !== task_id) {
            console.error("Task ID mismatch:", task.task_id, "vs", task_id);
            return respErr("unauthorized");
        }

        if (code === 200) {
            // 任务成功：直接更新数据库任务状态
            const task_result = data?.info?.resultUrls[0];
            const updateResult = await updateTaskStatusIdempotent(
                task_id,
                task.user_uuid,
                TaskStatus.COMPLETED,
                task_result
            );

            if (updateResult.success) {
                console.log(`Task ${task_id} updated to COMPLETED via callback`);
            } else {
                console.log(`Callback update skipped: ${updateResult.reason}`);
            }
        } else {
            // 任务失败：使用RPC函数同时更新状态和返还积分
            const refundTransType = task.task_type === TaskType.VEO3_FAST ? 
                CreditsTransType.Veo3FastGeneratorRefund : 
                CreditsTransType.Veo3GeneratorRefund;
            
            const failureResult = await handleTaskFailure({
                task_id: task.id!,
                user_uuid: task.user_uuid,
                refund_credits: task.task_cost_credit,
                refund_trans_type: refundTransType,
                error_info: `Callback notification: ${JSON.stringify(body)}`
            });
            
            if (failureResult.success) {
                console.log(`Task ${task_id} updated to FAILED and credits refunded via callback`);
            } else {
                console.log(`Task ${task_id} failure handling failed: ${failureResult.message}`);
                // Fallback: 如果RPC失败，至少更新任务状态
                const updateResult = await updateTaskStatusIdempotent(
                    task_id,
                    task.user_uuid,
                    TaskStatus.FAILED,
                    JSON.stringify(body)
                );
                
                if (updateResult.success) {
                    console.log(`Task ${task_id} status updated to FAILED (fallback), but credits may need manual handling`);
                } else {
                    console.log(`Task ${task_id} fallback status update also failed: ${updateResult.reason}`);
                }
            }
        }
        
        return respOk();
        
    } catch (error) {
        console.error("Veo3 callback error:", error);
        console.error("Veo3 callback body:", JSON.stringify(req.json));
        return respErr("internal server error");
    }
}
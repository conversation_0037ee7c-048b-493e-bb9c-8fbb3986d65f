import { respData, respErr, respOk } from "@/lib/resp";
import { getUserUuid } from "@/services/user";
import { 
  findPromptFavoriteByUuid, 
  updatePromptFavorite, 
  deletePromptFavorite 
} from "@/models/prompt-favorite";

export const runtime = "edge";

// 获取单个收藏详情
export async function GET(
  req: Request,
  { params }: { params: Promise<{ uuid: string }> }
) {
  const { uuid } = await params;
  try {
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("no auth");
    }
    if (!uuid) {
      return respErr("uuid is required");
    }

    const favorite = await findPromptFavoriteByUuid(uuid, user_uuid);
    if (!favorite) {
      return respErr("prompt favorite not found");
    }

    return respData(favorite);
  } catch (e) {
    console.log("get prompt favorite failed:", e);
    return respErr("get prompt favorite failed");
  }
}

// 更新收藏
export async function PUT(
  req: Request,
  { params }: { params: Promise<{ uuid: string }> }
) {
  const { uuid } = await params;
  try {
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("no auth");
    }
    if (!uuid) {
      return respErr("uuid is required");
    }

    const { category_uuid, title, notes, prompt } = await req.json();

    const favorite = await updatePromptFavorite(uuid, user_uuid, {
      category_uuid,
      title,
      notes,
      prompt
    });

    if (!favorite) {
      return respErr("prompt favorite not found");
    }

    return respData(favorite);
  } catch (e) {
    console.log("update prompt favorite failed:", e);
    if (e instanceof Error) {
      return respErr(e.message);
    }
    return respErr("update prompt favorite failed");
  }
}

// 删除收藏
export async function DELETE(
  req: Request,
  { params }: { params: Promise<{ uuid: string }> }
) {
  const { uuid } = await params;
  try {
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("no auth");
    }
    if (!uuid) {
      return respErr("uuid is required");
    }

    await deletePromptFavorite(uuid, user_uuid);

    return respOk();
  } catch (e) {
    console.log("delete prompt favorite failed:", e);
    return respErr("delete prompt favorite failed");
  }
} 
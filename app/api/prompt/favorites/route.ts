import { respData, respErr } from "@/lib/resp";
import { getUserUuid } from "@/services/user";
import { getUserPromptFavorites, getUserPromptFavoritesTotal, createPromptFavorite } from "@/models/prompt-favorite";
import { getUserPromptCategories } from "@/models/prompt-category";

export const runtime = "edge";

export async function GET(req: Request) {
  try {
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("no auth");
    }

    const url = new URL(req.url);
    const page = parseInt(url.searchParams.get("page") || "1");
    const limit = parseInt(url.searchParams.get("limit") || "20");
    const category_uuid = url.searchParams.get("category_uuid") || undefined;
    const keyword = url.searchParams.get("keyword");

    let favorites;
    let total;

    if (keyword) {
      // 如果有搜索关键词，使用搜索接口
      const { searchUserPromptFavorites } = await import("@/models/prompt-favorite");
      favorites = await searchUserPromptFavorites(user_uuid, keyword, page, limit);
      // 搜索时的总数可能不准确，这里简化处理
      total = favorites.length;
    } else {
      // 正常查询
      favorites = await getUserPromptFavorites(user_uuid, category_uuid, page, limit);
      total = await getUserPromptFavoritesTotal(user_uuid, category_uuid);
    }

    // 同时返回用户的分类列表，方便前端使用
    const categories = await getUserPromptCategories(user_uuid);

    return respData({
      favorites,
      categories,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (e) {
    console.log("get prompt favorites failed:", e);
    return respErr("get prompt favorites failed");
  }
}

export async function POST(req: Request) {
  try {
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("no auth");
    }

    const { category_uuid, prompt, model_name, title, notes } = await req.json();

    // 验证必填参数
    if (!prompt) {
      return respErr("prompt is required");
    }

    const favorite = await createPromptFavorite(user_uuid, {
      category_uuid,
      prompt,
      model_name,
      title,
      notes
    });

    return respData(favorite);
  } catch (e) {
    console.log("create prompt favorite failed:", e);
    if (e instanceof Error) {
      return respErr(e.message);
    }
    return respErr("create prompt favorite failed");
  }
} 
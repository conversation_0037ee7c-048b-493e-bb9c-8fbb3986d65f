import { respData, respErr, respOk } from "@/lib/resp";
import { getUserUuid } from "@/services/user";
import { 
  findPromptCategoryByUuid, 
  updatePromptCategory, 
  deletePromptCategory 
} from "@/models/prompt-category";

export const runtime = "edge";

// 获取单个分类详情
export async function GET(
  req: Request,
  { params }: { params: Promise<{ uuid: string }> }
) {
  const { uuid } = await params;
  try {
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("no auth");
    }

    if (!uuid) {
      return respErr("uuid is required");
    }

    const category = await findPromptCategoryByUuid(uuid, user_uuid);
    if (!category) {
      return respErr("category not found");
    }

    return respData(category);
  } catch (e) {
    console.log("get prompt category failed:", e);
    return respErr("get prompt category failed");
  }
}

// 更新分类
export async function PUT(
  req: Request,
  { params }: { params: Promise<{ uuid: string }> }
) {
  const { uuid } = await params;
  try {
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("no auth");
    }

    if (!uuid) {
      return respErr("uuid is required");
    }

    const { name } = await req.json();

    // 验证必填参数
    if (!name || !name.trim()) {
      return respErr("category name is required");
    }

    const category = await updatePromptCategory(uuid, user_uuid, {
      name: name.trim()
    });

    if (!category) {
      return respErr("category not found");
    }

    return respData(category);
  } catch (e) {
    console.log("update prompt category failed:", e);
    if (e instanceof Error) {
      return respErr(e.message);
    }
    return respErr("update prompt category failed");
  }
}

// 删除分类
export async function DELETE(
  req: Request,
  { params }: { params: Promise<{ uuid: string }> }
) {
  const { uuid } = await params;
  try {
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("no auth");
    }

    if (!uuid) {
      return respErr("uuid is required");
    }

    await deletePromptCategory(uuid, user_uuid);

    return respOk();
  } catch (e) {
    console.log("delete prompt category failed:", e);
    if (e instanceof Error) {
      return respErr(e.message);
    }
    return respErr("delete prompt category failed");
  }
} 
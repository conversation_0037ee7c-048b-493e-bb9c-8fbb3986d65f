import { respData, respErr } from "@/lib/resp";
import { getUserUuid } from "@/services/user";
import { getSupabaseClient } from "@/models/db";

export const runtime = "edge";

// 检查分类是否被使用
export async function GET(
  req: Request,
  { params }: { params: Promise<{ uuid: string }> }
) {
  const { uuid } = await params;
  try {
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("no auth");
    }

    if (!uuid) {
      return respErr("uuid is required");
    }

    const supabase = getSupabaseClient();
    
    // 检查是否还有收藏使用该分类
    const { data: favorites } = await supabase
      .from("user_prompt_favorites")
      .select("id")
      .eq("category_uuid", uuid)
      .eq("user_uuid", user_uuid)
      .limit(1);

    const hasUsage = favorites && favorites.length > 0;

    return respData({
      hasUsage,
      count: favorites?.length || 0
    });
  } catch (e) {
    console.log("check category usage failed:", e);
    return respErr("check category usage failed");
  }
} 
import { respData, respErr } from "@/lib/resp";
import { getUserUuid } from "@/services/user";
import { 
  getUserPromptCategories, 
  createPromptCategory,
  getUserPromptCategoriesTotal 
} from "@/models/prompt-category";

export const runtime = "edge";

// 获取用户分类列表
export async function GET(req: Request) {
  try {
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("no auth");
    }

    const url = new URL(req.url);
    const page = parseInt(url.searchParams.get("page") || "1");
    const limit = parseInt(url.searchParams.get("limit") || "50");

    const categories = await getUserPromptCategories(user_uuid, page, limit);
    const total = await getUserPromptCategoriesTotal(user_uuid);

    return respData({
      categories,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (e) {
    console.log("get prompt categories failed:", e);
    return respErr("get prompt categories failed");
  }
}

// 创建分类
export async function POST(req: Request) {
  try {
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("no auth");
    }

    const { name } = await req.json();

    // 验证必填参数
    if (!name || !name.trim()) {
      return respErr("name is required");
    }

    // 验证名称长度
    if (name.trim().length > 50) {
      return respErr("name must be less than 50 characters");
    }

    const category = await createPromptCategory(user_uuid, {
      name: name.trim()
    });

    return respData(category);
  } catch (e) {
    console.log("create prompt category failed:", e);
    
    // 处理特定的重复名称错误
    if (e instanceof Error && e.message.includes("already exists")) {
      return respErr(e.message);
    }
    
    if (e instanceof Error) {
      return respErr(e.message);
    }
    return respErr("create prompt category failed");
  }
} 
import { getUserEmail, getUserUuid } from "@/services/user";
import { insertOrder, updateOrderSession } from "@/models/order";
import { respData, respErr } from "@/lib/resp";

import { Order } from "@/types/order";
import Strip<PERSON> from "stripe";
import { findUserByUuid } from "@/models/user";
import { getSnowId } from "@/lib/hash";
import { getPricingPage } from "@/services/page";
import { PricingItem } from "@/types/blocks/pricing";

export async function POST(req: Request) {
  try {
    let {
      credits,
      currency,
      amount,
      interval,
      product_id,
      product_name,
      valid_months,
      cancel_url,
    } = await req.json();

    if (!cancel_url) {
      cancel_url = `${
        process.env.NEXT_PUBLIC_PAY_CANCEL_URL ||
        process.env.NEXT_PUBLIC_WEB_URL
      }`;
    }

    // 验证必需参数
    if (!interval || !currency || !product_id) {
      return respErr("invalid params");
    }

    // 验证数据类型和范围
    if (typeof amount !== "number" || amount < 0) {
      return respErr("invalid amount");
    }

    if (credits !== undefined && (typeof credits !== "number" || credits < 0)) {
      return respErr("invalid credits");
    }

    if (valid_months !== undefined && (typeof valid_months !== "number" || valid_months <= 0)) {
      return respErr("invalid valid_months");
    }

    // validate checkout params - 使用英文作为权威定价数据源
    const page = await getPricingPage("en");
    if (!page || !page.pricing || !page.pricing.items) {
      return respErr("invalid pricing table");
    }
    // console.log("page.pricing.items", page.pricing.items);

    const item = page.pricing.items.find(
      (item: PricingItem) => item.product_id === product_id
    );
    
    // 验证基础参数
    if (!item || !item.interval || item.interval !== interval) {
      console.log("invalid product or interval", item, interval);
      return respErr("invalid checkout params");
    }

    // 验证价格参数
    let validAmount = false;
    let validCurrency = false;
    let validCredits = false;

    // 人民币支付验证
    if (currency === "cny") {
      validAmount = item.cn_amount === amount;
      validCurrency = true; // cny currency is always valid when cn_amount exists
      if (!item.cn_amount) {
        console.log("cn_amount not available for product", product_id);
        return respErr("Chinese payment not available for this product");
      }
    } else {
      // 其他货币支付验证
      validAmount = item.amount === amount;
      validCurrency = item.currency === currency;
    }

    // 验证积分数量
    if (item.credits !== undefined) {
      validCredits = item.credits === credits;
    } else {
      validCredits = credits === 0 || credits === undefined;
    }

    if (!validAmount || !validCurrency || !validCredits) {
      console.log("invalid checkout params", {
        item_amount: item.amount,
        item_cn_amount: item.cn_amount,
        item_currency: item.currency,
        item_credits: item.credits,
        request_amount: amount,
        request_currency: currency,
        request_credits: credits,
        validAmount,
        validCurrency,
        validCredits
      });
      return respErr("invalid checkout params");
    }

    // 验证有效月数
    if (item.valid_months !== undefined && item.valid_months !== valid_months) {
      console.log("invalid valid_months", item.valid_months, valid_months);
      return respErr("invalid valid_months");
    }

    if (!["year", "month", "one-time"].includes(interval)) {
      return respErr("invalid interval");
    }

    const is_subscription = interval === "month" || interval === "year";

    // valid_months 验证已在上面的统一验证中处理

    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("no auth, please sign-in");
    }

    let user_email = await getUserEmail();
    if (!user_email) {
      const user = await findUserByUuid(user_uuid);
      if (user) {
        user_email = user.email;
      }
    }
    if (!user_email) {
      return respErr("invalid user");
    }

    const order_no = getSnowId();

    const currentDate = new Date();
    const created_at = currentDate.toISOString();

    let expired_at = "";

    const timePeriod = new Date(currentDate);
    timePeriod.setMonth(currentDate.getMonth() + valid_months);

    const timePeriodMillis = timePeriod.getTime();
    let delayTimeMillis = 0;

    // subscription
    if (is_subscription) {
      delayTimeMillis = 24 * 60 * 60 * 1000; // delay 24 hours expired
    }

    const newTimeMillis = timePeriodMillis + delayTimeMillis;
    const newDate = new Date(newTimeMillis);

    expired_at = newDate.toISOString();

    const order: Order = {
      order_no: order_no,
      created_at: created_at,
      user_uuid: user_uuid,
      user_email: user_email,
      amount: amount,
      interval: interval,
      expired_at: expired_at,
      status: "created",
      credits: credits,
      currency: currency,
      product_id: product_id,
      product_name: product_name,
      valid_months: valid_months,
    };
    await insertOrder(order);

    const stripe = new Stripe(process.env.STRIPE_PRIVATE_KEY || "");

    let options: Stripe.Checkout.SessionCreateParams = {
      payment_method_types: ["card"],
      line_items: [
        {
          price_data: {
            currency: currency,
            product_data: {
              name: product_name,
            },
            unit_amount: amount,
            recurring: is_subscription
              ? {
                  interval: interval,
                }
              : undefined,
          },
          quantity: 1,
        },
      ],
      allow_promotion_codes: true,
      metadata: {
        project: process.env.NEXT_PUBLIC_PROJECT_NAME || "",
        product_name: product_name,
        order_no: order_no.toString(),
        user_email: user_email,
        credits: credits,
        user_uuid: user_uuid,
      },
      mode: is_subscription ? "subscription" : "payment",
      success_url: `${process.env.NEXT_PUBLIC_WEB_URL}/pay-success/{CHECKOUT_SESSION_ID}`,
      cancel_url: cancel_url,
    };

    if (user_email) {
      options.customer_email = user_email;
    }

    if (is_subscription) {
      options.subscription_data = {
        metadata: options.metadata,
      };
    }

    if (currency === "cny") {
      options.payment_method_types = ["wechat_pay", "alipay", "card"];
      options.payment_method_options = {
        wechat_pay: {
          client: "web",
        },
        alipay: {},
      };
    }

    const order_detail = JSON.stringify(options);

    const session = await stripe.checkout.sessions.create(options);

    const stripe_session_id = session.id;
    await updateOrderSession(order_no, stripe_session_id, order_detail);

    return respData({
      public_key: process.env.STRIPE_PUBLIC_KEY,
      order_no: order_no,
      session_id: stripe_session_id,
    });
  } catch (e: any) {
    console.log("checkout failed: ", e);
    return respErr("checkout failed: " + e.message);
  }
}

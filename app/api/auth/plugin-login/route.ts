import { NextRequest, NextResponse } from "next/server";
import { validateGoogleToken } from "@/services/google-token-validator";
import { saveUser } from "@/services/user";
import { findChromeExtensionApiKey, insertApikey, ApikeyStatus } from "@/models/apikey";
import { getUuid, getNonceStr } from "@/lib/hash";
import { getIsoTimestr } from "@/lib/time";
import { getClientIp } from "@/lib/ip";

interface PluginLoginRequest {
  token: string;
  extension_id?: string;
  extension_version?: string;
}

interface PluginLoginResponse {
  success: boolean;
  data?: {
    api_key: string;
    user: {
      uuid: string;
      email: string;
      nickname: string;
      avatar_url: string;
    };
  };
  error?: string;
}

export async function POST(req: NextRequest): Promise<NextResponse<PluginLoginResponse>> {
  try {
    const body = await req.json();
    const { token, extension_id, extension_version } = body as PluginLoginRequest;
    
    // 验证输入参数
    if (!token || typeof token !== 'string') {
      return NextResponse.json({
        success: false,
        error: 'Invalid token format'
      }, { status: 400 });
    }
    
    if (token.length > 2048) {
      return NextResponse.json({
        success: false,
        error: 'Token too long'
      }, { status: 400 });
    }
    
    // 验证Google Token
    const googleUser = await validateGoogleToken(token);
    if (!googleUser) {
      return NextResponse.json({
        success: false,
        error: 'Invalid Google token'
      }, { status: 401 });
    }

    console.log('googleUser', googleUser);
    
    // 创建或查找用户
    const user = await saveUser({
      uuid: getUuid(),
      email: googleUser.email,
      nickname: googleUser.name || '',
      avatar_url: googleUser.picture || '',
      signin_type: 'oauth',
      signin_provider: 'google',
      signin_openid: googleUser.sub,
      signin_source: 'chrome_extension',
      signin_ip: await getClientIp(),
      created_at: getIsoTimestr(),
      invite_code: '',
      invited_by: '',
      is_affiliate: false,
    });
    
    if (!user || !user.uuid) {
      return NextResponse.json({
        success: false,
        error: 'Failed to create or find user'
      }, { status: 500 });
    }
    
    // 查找现有的Chrome Extension API key
    let apiKey = await findChromeExtensionApiKey(user.uuid);
    
    // 如果没有，创建新的API key
    if (!apiKey) {
      const newApiKey = {
        user_uuid: user.uuid,
        api_key: `sk-${getNonceStr(32)}`,
        title: 'Chrome Extension',
        created_at: getIsoTimestr(),
        status: ApikeyStatus.Created,
      };
      
      await insertApikey(newApiKey);
      apiKey = newApiKey.api_key;
    }
    
    // 返回成功响应
    return NextResponse.json({
      success: true,
      data: {
        api_key: apiKey,
        user: {
          uuid: user.uuid,
          email: user.email,
          nickname: user.nickname,
          avatar_url: user.avatar_url,
        }
      }
    });
    
  } catch (error) {
    console.error('Plugin login error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
} 
import { PromptFavoriteWithCategory } from "@/types/prompt-favorite";
import { PromptCategory } from "@/types/prompt-category";

export interface FavoritesSlotProps {
  favorites: PromptFavoriteWithCategory[];
  categories: PromptCategory[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
  selectedCategory?: string;
  searchKeyword?: string;
}

export interface PromptCardProps {
  favorite: PromptFavoriteWithCategory;
  onEdit: (favorite: PromptFavoriteWithCategory) => void;
  onDelete: (id: string) => void;
  onCopy: (content: string) => void;
  onView: (favorite: PromptFavoriteWithCategory) => void;
}

export interface CategoryNavProps {
  categories: PromptCategory[];
  selectedCategory?: string;
  onCategoryChange: (categoryId?: string) => void;
  onAddCategory: () => void;
  onEditCategory: (category: PromptCategory, newName: string) => Promise<void>;
  onDeleteCategory?: () => void;
  totalCount: number;
}

export interface SearchBarProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
}

export interface ToolbarProps {
  searchValue: string;
  onSearchChange: (value: string) => void;
  onAddNew: () => void;
  totalCount: number;
} 
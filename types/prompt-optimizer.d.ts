export interface PromptOptimizer {
  id: number;
  prompt?: string;
  model_type: 'text' | 'image' | 'video';
  model_name: string;
  language: string;
  version: string;
  status: 'offline' | 'online';
  created_at?: string;
  updated_at?: string;
}

export interface CreatePromptOptimizerRequest {
  prompt?: string;
  model_type: 'text' | 'image' | 'video';
  model_name: string;
  language?: string;
  version?: string;
  status?: 'offline' | 'online';
}

export interface UpdatePromptOptimizerRequest {
  prompt?: string;
  model_type?: 'text' | 'image' | 'video';
  model_name?: string;
  language?: string;
  version?: string;
  status?: 'offline' | 'online';
}

export interface PromptOptimizerFilters {
  model_type?: 'text' | 'image' | 'video';
  model_name?: string;
  language?: string;
  status?: 'offline' | 'online';
  version?: string;
} 
// Veo-3 JSON生成器相关类型定义

export interface Veo3Template {
  id: string;
  name: {
    en: string;
    zh: string;
  };
  description: {
    en: string;
    zh: string;
  };
  category: 'simple' | 'advanced';
  schema: TemplateField[];
  defaultValues: any;
  sampleVideo?: {
    coverImage: string;
    videoUrl: string;
    title: {
      en: string;
      zh: string;
    };
  };
}

export interface TemplateField {
  key: string;
  type: 'text' | 'textarea' | 'select' | 'array' | 'object';
  label: {
    en: string;
    zh: string;
  };
  placeholder?: {
    en: string;
    zh: string;
  };
  options?: string[];
  required?: boolean;
  children?: TemplateField[];
  defaultValue?: any;
}

export interface Veo3JsonPromptTemplateGeneratorConfig {
  title: string;
  subtitle: string;
  templateMode: string;
  aiMode: string;
  selectTemplate: string;
  generateJson: string;
  copyJson: string;
  inputPrompt: string;
  generateButton: string;
  generating: string;
  jsonPreview: string;
  sampleVideo: string;
  successCopied: string;
  errorCopyFailed: string;
  errorNoInput: string;
  errorGenerateFailed: string;
  successGenerated: string;
}

export interface Veo3JsonPromptAIGeneratorConfig {
    inputPrompt: string;
    generateButton: string;
    generating: string;
    jsonPreview: string;
    copyJson: string;
    successGenerated: string;
    successCopied: string;
    errorNoInput: string;
    errorGenerateFailed: string;
    errorCopyFailed: string;
    advancedGenerating: string;
    advancedGenerateButton: string;
    basicModeLabel: string;
    advancedModeLabel: string;
    basicModeHint: string;
    advancedModeHint: string;
    errorNotLoggedIn: string;
    errorInsufficientCredits: string;
    creditsRemaining: string;
    creditsInsufficient: string;
    buyCredits: string;
    inputPlaceholder: string;
    emptyStateText: string;
}

export interface Veo3FormData {
  [key: string]: any;
}

export type Veo3Mode = 'template' | 'ai';

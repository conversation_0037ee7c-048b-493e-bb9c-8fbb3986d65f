export interface UserPromptFavorite {
  id: number;
  uuid: string;
  user_uuid: string;
  category_uuid?: string;
  prompt: string;
  model_name?: string;
  title?: string;
  notes?: string;
  created_at?: string;
  updated_at?: string;
}

export interface CreatePromptFavoriteRequest {
  category_uuid?: string;
  prompt: string;
  model_name?: string;
  title?: string;
  notes?: string;
}

export interface UpdatePromptFavoriteRequest {
  category_uuid?: string;
  prompt?: string;
  title?: string;
  notes?: string;
}

export interface PromptFavoriteWithCategory extends UserPromptFavorite {
  category_name?: string;
} 
export interface PromptExample {
  id: string;
  title: string;        // 例子标题
  videoUrl: string;     // 视频链接
  thumbnail?: string;   // 视频缩略图（可选）
  prompt: string;       // 提示词内容
}

export interface PromptExamplesSection {
  title: string;          // 主标题
  subtitle?: string;      // 副标题
  items: PromptExample[]; // 示例列表
}

export interface PromptExamplesConfig {
  copyButton: string;     // 复制按钮文案
  successCopied: string;  // 复制成功提示
  errorCopyFailed: string; // 复制失败提示
}

export interface PromptExamplesProps {
  section: PromptExamplesSection;
  config: PromptExamplesConfig;
  className?: string;
} 
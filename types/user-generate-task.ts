export interface UserGenerateTask {
  id?: number;
  user_uuid: string;
  task_type: string; // 'veo3_fast', 'veo3', etc.
  task_status: string; // 'pending', 'processing', 'completed', 'failed'
  task_result?: string; // 任务结果
  task_id: string; // 调用API返回的task id
  task_params?: string; // 任务参数
  task_cost_credit: number; // 任务消耗的积分
  callback_token?: string; // 回调验证token
  version: number; // 版本号，用于乐观锁控制
  started_at?: string;
  completed_at?: string;
  created_at?: string;
  updated_at?: string;
}

export interface CreateUserGenerateTaskRequest {
  user_uuid: string;
  task_type: string;
  task_id: string;
  task_params?: string;
  task_cost_credit: number;
  callback_token?: string;
}

export interface UpdateUserGenerateTaskRequest {
  task_status?: string;
  task_result?: string;
  started_at?: string;
  completed_at?: string;
  updated_at?: string;
}

export interface UserGenerateTaskFilters {
  user_uuid?: string;
  task_type?: string;
  task_status?: string;
  task_id?: string;
}

export enum TaskType {
  VEO3_FAST = 'veo3_fast',
  VEO3 = 'veo3',
}

export enum TaskStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
} 
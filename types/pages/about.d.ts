import { BaseSection } from "@/types/blocks/base";
import { Button } from "@/types/blocks/base";
import { HeroSection } from "@/types/blocks/hero";

export interface AboutHeroSection {
  title: string;
  description: string;
  image?: {
    src: string;
    alt: string;
  };
}

export interface AboutMissionVisionSection {
  title: string;
  description: string;
  icon: string;
}

export interface TeamFeature {
  title: string;
  description: string;
  icon: string;
}

export interface AboutTeamSection {
  title: string;
  description: string;
  features: TeamFeature[];
}

export interface AboutMetadata {
  title: string;
  description: string;
}

export interface AboutPage {
  metadata: AboutMetadata;
  hero?: AboutHeroSection;
  mission?: AboutMissionVisionSection;
  vision?: AboutMissionVisionSection;
  team?: AboutTeamSection;
} 
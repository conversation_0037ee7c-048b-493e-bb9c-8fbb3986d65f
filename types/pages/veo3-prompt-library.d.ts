import { Head<PERSON> } from "@/types/blocks/header";
import { Hero } from "@/types/blocks/hero";
import { Section } from "@/types/blocks/section";
import { Footer } from "@/types/blocks/footer";
import { PromptExamplesSection } from "@/types/blocks/prompt-examples";

export interface Veo3PromptLibraryPage {
  title?: string;
  description?: string;
  header?: Header;
  hero?: Hero;
  examples?: PromptExamplesSection;
  cta?: Section;
  footer?: Footer;
} 
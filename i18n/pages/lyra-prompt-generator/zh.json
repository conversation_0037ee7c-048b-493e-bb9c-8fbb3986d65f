{"template": "shipany-template-one", "theme": "light", "title": "Lyra提示词生成器 - Reddit爆火的AI提示词优化神器 | Prompt Ark", "description": "体验Reddit爆火的Lyra提示词技术，将模糊想法转化为精准AI指令。600万浏览量验证的提示词优化方法，让AI真正理解你的需求。", "header": {"brand": {"title": "Prompt Ark", "logo": {"src": "/logo.png", "alt": "Prompt Ark"}, "url": "/"}, "nav": {"items": [{"title": "Lyra提示词生成器", "url": "/lyra-prompt/#generator", "icon": "RiMagicLine"}, {"title": "提示词库", "url": "/prompt-library", "icon": "RiBookLine"}]}}, "hero": {"title": "Lyra提示词生成器：Reddit爆火的AI对话革命", "highlight_text": "Lyra提示词生成器", "description": "源自Reddit的病毒式传播技术，600万浏览量验证效果。<br/>不再猜测AI需要什么，让Lyra反向采访你，生成完美提示词。", "buttons": [{"title": "体验Lyra技术", "icon": "RiMagicLine", "url": "/lyra-prompt/#generator", "target": "_self", "variant": "default"}, {"title": "了解原理", "icon": "RiBookOpenLine", "url": "https://www.reddit.com/r/ChatGPT/comments/1lnfcnt/after_147_failed_chatgpt_prompts_i_had_a/", "target": "_self", "variant": "outline"}]}, "introduce": {"name": "introduce", "title": "什么是Lyra提示词？Reddit现象级突破的故事", "label": "起源故事", "description": "Lyra提示词诞生于一个沮丧的Reddit用户在147次失败尝试后的灵光一现。这种元提示词技术颠覆了AI交互模式——不再猜测AI需要什么，而是让Lyra先采访你。", "items": [{"title": "Reddit现象级传播", "description": "凌晨3点的灵感突破，Lyra提示词在Reddit获得600万+浏览量和6万次分享，成为病毒式传播现象。", "icon": "RiFireLine"}, {"title": "4D方法论体系", "description": "解构、诊断、开发、交付——Lyra独创的四步优化框架，让模糊想法变成精准指令。", "icon": "RiMagicLine"}, {"title": "反向采访机制", "description": "颠覆传统提示词模式，Lyra主动提问收集上下文，然后自动生成最优提示词。", "icon": "RiBrainLine"}]}, "benefit": {"name": "benefit", "title": "为什么Lyra提示词优化能改变一切", "label": "核心优势", "description": "体验从泛泛而谈到精准命中的差别，感受Lyra智能提示词优化带来的质的飞跃。", "items": [{"title": "从模糊到精准", "description": "将\"写个销售邮件\"转化为个性化、高转化的文案，让Lyra先问对问题再给出答案。", "icon": "RiMagicLine", "image": {"src": "/imgs/features/2.png", "alt": "Prompt Ark - 高级提示词工程"}}, {"title": "全平台通用", "description": "针对ChatGPT、<PERSON>、Gemini等AI模型优化，采用平台专属技术和最佳实践。", "icon": "RiRobot2Line", "image": {"src": "/imgs/features/3.png", "alt": "Prompt Ark - AI提示词生成器即时结果"}}, {"title": "智能上下文收集", "description": "Lyra通过巧妙提问理解你的确切需求，然后自动构建完美提示词。", "icon": "RiBrainLine", "image": {"src": "/imgs/features/4.png", "alt": "Prompt Ark - 提示词工程专家支持"}}]}, "usage": {"name": "usage", "title": "如何使用Lyra提示词：从挫败到成功", "description": "掌握Lyra提示词技术的四个简单步骤，加入数百万用户改变AI交互的行列：", "image_position": "left", "text_align": "center", "items": [{"title": "说出你的模糊想法", "description": "随便说个大概想法——\"帮我做饭\"、\"写营销文案\"、\"调试代码\"，Lyra会处理剩下的。"}, {"title": "回答Lyra的巧妙提问", "description": "Lyra会问2-3个针对性问题，了解你的具体情境、受众和要求。"}, {"title": "获得优化后的提示词", "description": "收到运用Lyra 4D方法论和高级优化技术精心制作的提示词。"}, {"title": "在任何AI平台使用", "description": "将优化后的提示词复制到ChatGPT、<PERSON>、Gemini或任何AI工具，体验显著提升的效果。"}]}, "feature": {"name": "feature", "title": "Lyra提示词功能：让高级AI优化变得简单", "description": "掌握病毒式传播的Lyra方法论所需的一切工具。", "items": [{"title": "4D方法论系统", "description": "解构、诊断、开发、交付——Lyra经过验证的框架，已改变数百万次AI交互。", "icon": "RiMagicLine"}, {"title": "多平台优化", "description": "针对ChatGPT、<PERSON>、Gemini等AI平台的专门技术和平台专属最佳实践。", "icon": "RiRobot2Line"}, {"title": "智能上下文采集", "description": "Lyra主动提问收集关键信息，确保生成的提示词完全符合你的需求。", "icon": "RiBrainLine"}, {"title": "双模式选择", "description": "基础模式快速修复核心问题，详细模式提供全面优化和澄清问题，满足不同需求。", "icon": "RiSettings3Line"}, {"title": "高级提示词技术", "description": "内置思维链、少样本学习、多角度分析和约束优化等先进技术，自动应用最佳实践。", "icon": "RiToolsLine"}, {"title": "实时优化反馈", "description": "即时显示优化效果和改进说明，提供专业建议让你了解每次优化的价值。", "icon": "RiFlashlightLine"}]}, "stats": {"disabled": false, "name": "stats", "label": "病毒式影响", "title": "Lyra提示词：从Reddit帖子到全球现象", "description": "病毒式AI优化突破背后的数据", "icon": "RiFireLine", "items": [{"title": "Reddit浏览量", "label": "600万+", "description": "病毒式传播"}, {"title": "社交分享", "label": "6万+", "description": "社区分享"}, {"title": "成功案例", "label": "1000+", "description": "用户转变"}]}, "faq": {"name": "faq", "label": "常见问题", "title": "关于Lyra提示词的常见问题", "description": "了解这个改变数百万人AI交互方式的病毒式优化技术的一切。", "items": [{"title": "Lyra提示词到底是什么？为什么会爆火？", "description": "Lyra提示词是一种元提示词优化技术，源自一个沮丧的Reddit用户在147次失败尝试后的突破。这项技术获得600万+浏览量是因为它从根本上改变了我们与AI的交互方式——不再猜测AI需要什么，而是让Lyra先采访你来创建精准提示词。"}, {"title": "Lyra提示词与普通提示词工程有什么不同？", "description": "传统提示词工程需要你预先知道要包含什么信息。Lyra颠覆了这种模式，它充当智能采访者，提出针对性问题收集上下文，然后应用4D方法论（解构、诊断、开发、交付）自动创建优化提示词。"}, {"title": "Lyra提示词适用于所有AI平台还是只有ChatGPT？", "description": "Lyra提示词设计为适用于所有主流AI平台，包括ChatGPT、<PERSON>、Gemini等。它包含平台专属优化技术——比如ChatGPT的结构化部分、<PERSON>的长上下文框架、Gemini的创意任务优化。"}, {"title": "详细模式和基础模式在Lyra中有什么区别？", "description": "详细模式通过2-3个巧妙的澄清问题收集全面上下文，提供完整优化和技术说明。基础模式快速修复主要问题，仅应用核心技术以获得即时结果。根据你的时间和复杂性需求选择。"}, {"title": "初学者能否在没有提示词工程知识的情况下有效使用Lyra？", "description": "当然可以！这正是Lyra的魅力所在——它由一个对传统提示词工程感到沮丧的人创造。你只需用简单的语言描述想要什么，Lyra会处理所有技术优化。无需任何提示词工程经验。"}, {"title": "使用Lyra提示词优化能期待什么样的效果？", "description": "用户反馈显示AI回答质量显著提升——从泛泛而谈的模板到个性化、可操作的内容。包括转化率更高的销售邮件、个性化饮食计划、有效的调试提示词，甚至全面的婚礼策划建议。关键在于Lyra能先收集正确的上下文。"}, {"title": "这就是Min Choi在Twitter上分享的那个Lyra提示词吗？", "description": "是的！Reddit帖子爆火后，Twitter影响者Min Choi分享了Lyra提示词技术，创造了第二波病毒式传播。我们的生成器实现了原始病毒帖子的完全相同方法论，现在以交互式工具的形式提供。"}, {"title": "如何开始使用Lyra提示词优化？", "description": "只需使用上面的Lyra提示词生成器。输入任何模糊请求，比如\"帮我写更好的邮件\"或\"规划我的锻炼计划\"，让Lyra采访你来创建完美的优化提示词。然后将结果复制到任何AI平台。"}]}, "cta": {"name": "cta", "title": "立即体验病毒式传播的Lyra提示词技术", "description": "加入数百万用户，用革命性的提示词优化方法改变你的AI交互。", "buttons": [{"title": "体验Lyra提示词", "url": "/lyra-prompt/#generator", "target": "_self", "icon": "RiMagicLine"}]}}
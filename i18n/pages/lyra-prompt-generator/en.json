{"template": "shipany-template-one", "theme": "light", "title": "<PERSON>yra Prompt - Transform Vague Ideas into Powerful AI Prompts", "description": "Discover <PERSON>yra Prompt, the viral Reddit prompt optimization technique that transforms simple requests into precision-crafted AI prompts. Get better ChatGPT responses instantly.", "header": {"brand": {"title": "Prompt Ark", "logo": {"src": "/logo.png", "alt": "Prompt Ark"}, "url": "/"}, "nav": {"items": [{"title": "<PERSON><PERSON> Prompt", "url": "/lyra-prompt", "icon": "RiMagicLine"}, {"title": "Prompt Library", "url": "/prompt-library", "icon": "RiBookLine"}]}}, "hero": {"title": "Lyra Prompt: The Viral AI Optimization Technique", "highlight_text": "<PERSON><PERSON> Prompt", "description": "Transform your vague AI requests into precision-crafted prompts using the revolutionary Lyra methodology.<br/>From Reddit viral sensation to 6+ million views - now available as an interactive generator.", "buttons": [{"title": "Try Lyra Prompt", "icon": "RiMagicLine", "url": "/lyra-prompt/#generator", "target": "_self", "variant": "default"}, {"title": "Learn More", "icon": "RiBookOpenLine", "url": "https://www.reddit.com/r/ChatGPT/comments/1lnfcnt/after_147_failed_chatgpt_prompts_i_had_a/", "target": "_self", "variant": "outline"}]}, "introduce": {"name": "introduce", "title": "What is <PERSON><PERSON> Prompt? The Story Behind the Viral AI Breakthrough", "label": "Origin Story", "description": "Lyra Prompt started as a frustrated Reddit user's breakthrough after 147 failed ChatGPT attempts. This meta-prompt technique flips the AI interaction model - instead of guessing what AI needs, <PERSON><PERSON> interviews YOU first.", "items": [{"title": "The Reddit Phenomenon", "description": "Born from frustration at 3 AM, <PERSON><PERSON> became a viral sensation with 6+ million views and 60,000 shares on Reddit.", "icon": "RiFireLine"}, {"title": "The 4-D Methodology", "description": "Deconstruct, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deliver - Lyra's systematic approach to prompt optimization that works across all AI platforms.", "icon": "RiMagicLine"}]}, "benefit": {"name": "benefit", "title": "Why Lyra Prompt Optimization Changes Everything", "label": "Benefits", "description": "Experience the difference between generic AI responses and precision-crafted results with Lyra's intelligent prompt optimization.", "items": [{"title": "From Vague to Precise", "description": "Transform 'Write a sales email' into personalized, converting copy by letting <PERSON><PERSON> ask the right clarifying questions first.", "icon": "RiMagicLine", "image": {"src": "/imgs/features/2.png", "alt": "Prompt Ark - Advanced Prompt Engineering"}}, {"title": "Works Across All AI Platforms", "description": "Optimized for ChatGPT, Claude, Gemini, and other AI models with platform-specific techniques and best practices.", "icon": "RiRobot2Line", "image": {"src": "/imgs/features/3.png", "alt": "Prompt Ark - Instant Results with AI Prompt Generator"}}, {"title": "Intelligent Context Gathering", "description": "<PERSON><PERSON> interviews you with smart questions to understand your exact needs, then crafts the perfect prompt automatically.", "icon": "RiBrainLine", "image": {"src": "/imgs/features/4.png", "alt": "Prompt Ark - Expert Support for Prompt Engineering"}}]}, "usage": {"name": "usage", "title": "How to Use Lyra Prompt: From Frustration to Success", "description": "Master the <PERSON><PERSON> Prompt technique in four simple steps and join millions who've transformed their AI interactions:", "image_position": "left", "text_align": "center", "items": [{"title": "Share Your Vague Request", "description": "Start with any rough idea - 'help with meal prep', 'write marketing copy', or 'debug my code'. <PERSON><PERSON> handles the rest."}, {"title": "Answer Lyra's Smart Questions", "description": "L<PERSON> asks 2-3 targeted questions to understand your specific context, audience, and requirements."}, {"title": "Get Your Optimized Prompt", "description": "Receive a precision-crafted prompt using Lyra's 4-D methodology and advanced optimization techniques."}, {"title": "Use Across Any AI Platform", "description": "Copy your optimized prompt to <PERSON>tGPT, Claude, Gemini, or any AI tool and experience dramatically better results."}]}, "feature": {"name": "feature", "title": "Lyra Prompt Features: Advanced AI Optimization Made Simple", "description": "Everything you need to master prompt engineering with the viral Lyra methodology.", "items": [{"title": "4-D Methodology System", "description": "Deconstruct, Di<PERSON>nose, <PERSON><PERSON><PERSON>, Deliver - Lyra's proven framework that transformed millions of AI interactions.", "icon": "RiMagicLine"}, {"title": "Multi-Platform Optimization", "description": "Specialized techniques for ChatGPT, Claude, Gemini, and other AI platforms with platform-specific best practices.", "icon": "RiRobot2Line"}, {"title": "Intelligent Context Gathering", "description": "Smart questioning system that extracts exactly what's needed for optimal prompt construction.", "icon": "RiBrainLine"}, {"title": "DETAIL vs BASIC Modes", "description": "Choose comprehensive optimization with clarifying questions or quick fixes for immediate results.", "icon": "RiSettings3Line"}, {"title": "Advanced Prompt Techniques", "description": "Chain-of-thought, few-shot learning, multi-perspective analysis, and constraint optimization built-in.", "icon": "RiToolsLine"}, {"title": "Real-Time Optimization", "description": "Get instant feedback on what changed and why, with pro tips for maximum effectiveness.", "icon": "RiFlashlightLine"}]}, "stats": {"disabled": false, "name": "stats", "label": "Viral Impact", "title": "<PERSON>yra Prompt: From Reddit Post to Global Phenomenon", "description": "The numbers behind the viral AI optimization breakthrough", "icon": "RiFireLine", "items": [{"title": "Reddit Views", "label": "6M+", "description": "Viral Views"}, {"title": "Social Shares", "label": "60K+", "description": "Community Shares"}, {"title": "Success Stories", "label": "1000+", "description": "User Transformations"}]}, "pricing": {"name": "pricing", "label": "Pricing", "title": "Choose Your Perfect Plan", "description": "Start free and get 10x productivity boost", "groups": [{"name": "subscribe-yearly", "title": "Yearly (Save 30%)"}, {"name": "subscribe-monthly", "title": "Monthly"}], "items": [{"group": "subscribe-yearly", "title": "Free Plan", "description": "Try before you commit. Ideal for light usage.", "features_title": "What's included", "features": ["Basic prompt generation (worth $3/month)", "Access to community support"], "interval": "year", "amount": 0, "currency": "", "price": "Free", "original_price": "", "unit": "", "is_featured": false, "tip": "🎁 Forever free", "product_id": "", "product_name": "", "credits": 0, "valid_months": 12}, {"group": "subscribe-yearly", "title": "Advanced Plan", "description": "1800 credits/year – great for regular prompt users", "features_title": "All Free Plan features, plus:", "features": ["Advanced prompt generation", "Faster processing queue", "Priority email support"], "interval": "year", "amount": 4192, "currency": "USD", "price": "41.92", "original_price": "59.88", "unit": "USD/year", "is_featured": false, "tip": "💰 Best for creators using prompts regularly", "button": {"title": "Upgrade Now", "url": "/#generator", "icon": "RiMagicLine"}, "product_id": "prod_STkXraIzq5zi8P", "product_name": "Yearly Subscription Advanced", "credits": 1800, "valid_months": 12}, {"group": "subscribe-yearly", "title": "Pro Plan", "description": "6000 credits/year – built for professionals & power users", "features_title": "All Advanced Plan features, plus:", "features": ["API access", "Top priority request processing", "Early access to new features", "Manage personal prompt collections"], "interval": "year", "amount": 8392, "currency": "USD", "price": "83.92", "original_price": "119.88", "unit": "USD/year", "is_featured": true, "tip": "🎖️ Best value for pros – save $36/year", "button": {"title": "Subscribe Now", "url": "/#generator", "icon": "RiMagicLine"}, "product_id": "prod_STkRIN5iI4x3LQ", "product_name": "Yearly Subscription Pro", "credits": 6000, "valid_months": 12}, {"group": "subscribe-monthly", "title": "Free Plan", "description": "Perfect for trying out and light usage", "features_title": "What's included", "features": ["Basic prompt generation (worth $3/month)", "Access to community support"], "interval": "month", "amount": 0, "currency": "", "price": "Free", "original_price": "", "unit": "", "is_featured": false, "tip": "🎁 Forever free", "product_id": "free", "product_name": "", "credits": 0, "valid_months": 1}, {"group": "subscribe-monthly", "title": "Advanced Plan", "description": "150 credits/month – great for moderate usage", "features_title": "All Free Plan features, plus:", "features": ["Advanced prompt generation", "Faster processing queue", "Priority email support"], "interval": "month", "amount": 499, "currency": "USD", "price": "4.99", "unit": "USD/month", "is_featured": false, "tip": "💰 Flexible monthly access", "button": {"title": "Upgrade Now", "url": "/#generator", "icon": "RiMagicLine"}, "product_id": "prod_STkbHMhZ5CunAo", "product_name": "Monthly Subscription Advanced", "credits": 150, "valid_months": 1}, {"group": "subscribe-monthly", "title": "Pro Plan", "description": "500 credits/month – designed for power users", "features_title": "All Advanced Plan features, plus:", "features": ["API access", "Top priority request processing", "Early access to new features", "Manage personal prompt collections"], "interval": "month", "amount": 999, "currency": "USD", "price": "9.99", "unit": "USD/month", "is_featured": true, "tip": "🎖️ Full access, full power", "button": {"title": "Subscribe Now", "url": "/#generator", "icon": "RiMagicLine"}, "product_id": "prod_STkZuduAicQbiT", "product_name": "Monthly Subscription Pro", "credits": 500, "valid_months": 1}]}, "testimonial": {"disabled": true, "name": "testimonial", "label": "Success Stories", "title": "Real Users, Real Results with Lyra Prompt", "description": "See how Lyra Prompt optimization transformed AI interactions for users worldwide.", "icon": "GoThumbsup", "items": [{"title": "<PERSON>", "label": "Content Creator", "description": "<PERSON><PERSON> Prompt completely changed my ChatGPT workflow. Instead of generic templates, I now get personalized content that actually converts. The difference is night and day!", "image": {"src": "/imgs/users/lyra-user-1.png"}}, {"title": "<PERSON>", "label": "Software Developer", "description": "I used <PERSON><PERSON> to debug code I didn't understand. It asked the right questions about my setup and goals, then gave me a perfect troubleshooting prompt. Saved me hours!", "image": {"src": "/imgs/users/lyra-user-2.png"}}, {"title": "<PERSON>", "label": "Marketing Manager", "description": "The meal prep example sold me - but I use Lyra for marketing campaigns. It helps me create targeted prompts that understand my audience's pain points perfectly.", "image": {"src": "/imgs/users/lyra-user-3.png"}}, {"title": "Michael <PERSON>", "label": "Wedding Planner", "description": "Someone mentioned using <PERSON><PERSON> for wedding planning in the Reddit thread. I tried it and it's incredible - personalized planning prompts based on budget, style, and guest count.", "image": {"src": "/imgs/users/lyra-user-4.png"}}]}, "faq": {"name": "faq", "label": "FAQ", "title": "Frequently Asked Questions About <PERSON>yra Prompt", "description": "Everything you need to know about the viral AI optimization technique that's transforming how people interact with AI.", "items": [{"title": "What exactly is <PERSON><PERSON> Prompt and how did it become viral?", "description": "Lyra Prompt is a meta-prompt optimization technique that originated from a frustrated Reddit user's breakthrough after 147 failed ChatGPT attempts. The technique went viral with 6+ million views because it fundamentally changes how we interact with AI - instead of guessing what AI needs, Lyra interviews you first to create precision-crafted prompts."}, {"title": "How is <PERSON><PERSON> Prompt different from regular prompt engineering?", "description": "Traditional prompt engineering requires you to know what information to include upfront. <PERSON><PERSON> flips this model by acting as an intelligent interviewer that asks targeted questions to gather context, then applies its 4-D methodology (Deconstruct, Diagnose, Develop, Deliver) to create optimized prompts automatically."}, {"title": "Does Lyra Prompt work with all AI platforms or just ChatGPT?", "description": "Lyra Prompt is designed to work across all major AI platforms including ChatGPT, Claude, Gemini, and others. It includes platform-specific optimization techniques - for example, structured sections for ChatGPT, longer context frameworks for <PERSON>, and creative task optimization for Gemini."}, {"title": "What's the difference between DETAIL and BASIC modes in Lyra?", "description": "DETAIL mode gathers comprehensive context through 2-3 smart clarifying questions and provides full optimization with techniques explanation. BASIC mode quickly fixes primary issues and applies core techniques only for immediate results. Choose based on your time and complexity needs."}, {"title": "Can beginners use Lyra Prompt effectively without prompt engineering knowledge?", "description": "Absolutely! That's the beauty of L<PERSON> - it was created by someone frustrated with traditional prompt engineering. You just need to describe what you want in plain language, and <PERSON><PERSON> handles all the technical optimization. No prior prompt engineering experience required."}, {"title": "What kind of results can I expect from using Lyra Prompt optimization?", "description": "Users report dramatically improved AI responses - from generic templates to personalized, actionable content. Examples include converting sales emails, personalized meal prep plans, effective debugging prompts, and even comprehensive wedding planning assistance. The key is <PERSON><PERSON>'s ability to gather the right context first."}, {"title": "Is this the same <PERSON>yra Prompt that <PERSON> shared on Twitter?", "description": "Yes! After the Reddit post went viral, Twitter influencer <PERSON> shared the Lyra Prompt technique, creating a second wave of viral adoption. Our generator implements the exact same methodology from the original viral post, now available as an interactive tool."}, {"title": "How do I get started with Lyra Prompt optimization?", "description": "Simply use our Lyra Prompt above. Enter any vague request like 'help me write better emails' or 'plan my workout routine', and let Lyra interview you to create the perfect optimized prompt. Then copy the result to any AI platform."}]}, "cta": {"name": "cta", "title": "Experience the Viral Lyra Prompt Technique Now", "description": "Join millions who've transformed their AI interactions with the revolutionary prompt optimization method.", "buttons": [{"title": "Try Lyra Prompt", "url": "/lyra-prompt/#generator", "target": "_self", "icon": "RiMagicLine"}]}, "footer": {"name": "footer", "brand": {"title": "Prompt Ark", "description": "Prompt Ark is the ultimate ChatGPT prompt generator for advanced prompt engineering. Create better GPT prompts that deliver superior ChatGPT results.", "logo": {"src": "/logo.png", "alt": "Prompt Ark"}, "url": "/"}, "copyright": "© 2025 • Prompt Ark All rights reserved.", "nav": {"items": [{"title": "About", "children": [{"title": "Contact", "url": "/contact", "target": "_self"}, {"title": "About Us", "url": "/about", "target": "_self"}]}, {"title": "Friends", "children": [{"title": "ListCompare", "url": "https://list-compare.net/", "target": "_blank"}]}]}, "agreement": {"items": [{"title": "Privacy Policy", "url": "/privacy-policy"}, {"title": "Terms of Service", "url": "/terms-of-service"}]}}}
-- PostgreSQL函数：处理Veo3任务事务（扣减积分+创建任务）
CREATE OR REPLACE FUNCTION handle_veo3_task_transaction(
  p_user_uuid VARCHAR(255),
  p_task_type VARCHAR(255),
  p_task_params TEXT,
  p_task_cost_credit INTEGER,
  p_callback_token VARCHAR(64),
  p_trans_type VARCHAR(50)
) RETURNS jsonb AS $$
DECLARE
  v_current_credits INTEGER := 0;
  v_task_id INTEGER;
  v_result jsonb;
  v_trans_no VARCHAR(255);
  v_order_no VARCHAR(255) := '';
  v_expired_at TIMESTAMPTZ;
  v_left_credits INTEGER := 0;
  v_credit_record RECORD;
BEGIN
  -- 锁定用户行，防止并发
  PERFORM 1 FROM users WHERE uuid = p_user_uuid FOR UPDATE;
  
  -- 计算当前积分并获取有效积分记录
  SELECT COALESCE(SUM(credits), 0) INTO v_current_credits
  FROM credits 
  WHERE user_uuid = p_user_uuid 
  AND (expired_at IS NULL OR expired_at > NOW());
  
  -- 检查积分是否足够
  IF v_current_credits < p_task_cost_credit THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'credits_not_enough',
      'current_credits', v_current_credits,
      'required_credits', p_task_cost_credit
    );
  END IF;
  
  -- 找到可用的积分记录来扣减（按过期时间排序）
  FOR v_credit_record IN 
    SELECT credits, order_no, expired_at 
    FROM credits 
    WHERE user_uuid = p_user_uuid 
    AND (expired_at IS NULL OR expired_at > NOW())
    ORDER BY expired_at ASC NULLS LAST
  LOOP
    v_left_credits := v_left_credits + v_credit_record.credits;
    
    IF v_left_credits >= p_task_cost_credit THEN
      v_order_no := v_credit_record.order_no;
      v_expired_at := v_credit_record.expired_at;
      EXIT;
    END IF;
  END LOOP;
  
  -- 生成事务号
  v_trans_no := 'trans_' || extract(epoch from now())::text || '_' || round(random() * 1000000)::text;
  
  -- 扣减积分
  INSERT INTO credits (
    trans_no, created_at, user_uuid, trans_type, credits, order_no, expired_at
  ) VALUES (
    v_trans_no,
    NOW(),
    p_user_uuid,
    p_trans_type,
    -p_task_cost_credit,
    v_order_no,
    v_expired_at
  );
  
  -- 创建任务
  INSERT INTO user_generate_tasks (
    user_uuid, task_type, task_status, task_id, task_params, 
    task_cost_credit, callback_token, version, started_at, created_at, updated_at
  ) VALUES (
    p_user_uuid,
    p_task_type,
    'processing',
    '', -- 外部任务ID稍后更新
    p_task_params,
    p_task_cost_credit,
    p_callback_token,
    1,
    NOW(),
    NOW(),
    NOW()
  ) RETURNING id INTO v_task_id;
  
  -- 返回成功结果
  RETURN jsonb_build_object(
    'success', true,
    'task_id', v_task_id,
    'remaining_credits', v_current_credits - p_task_cost_credit,
    'trans_no', v_trans_no
  );
  
EXCEPTION
  WHEN OTHERS THEN
    -- 出现任何错误都会自动回滚
    RETURN jsonb_build_object(
      'success', false,
      'error', 'transaction_failed',
      'message', SQLERRM
    );
END;
$$ LANGUAGE plpgsql;

-- PostgreSQL函数：处理任务失败（更新任务状态+返还积分）
-- 使用行级锁防止竞态条件和重复返还积分
CREATE OR REPLACE FUNCTION handle_task_failure(
  p_task_id INTEGER,
  p_user_uuid VARCHAR(255),
  p_refund_credits INTEGER,
  p_refund_trans_type VARCHAR(50),
  p_error_info TEXT DEFAULT NULL
) RETURNS jsonb AS $$
DECLARE
  v_task_record RECORD;
  v_trans_no VARCHAR(255);
BEGIN
  -- 🔒 使用行级锁锁定任务记录，防止并发访问
  SELECT * INTO v_task_record 
  FROM user_generate_tasks 
  WHERE id = p_task_id 
  AND user_uuid = p_user_uuid 
  FOR UPDATE;  -- 关键改动：添加行级锁
  
  -- 检查任务是否存在
  IF NOT FOUND THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'task_not_found'
    );
  END IF;
  
  -- 🔍 检查任务状态（已经被锁定，不会有竞态条件）
  -- 如果任务已经处理过，直接返回成功（幂等性）
  IF v_task_record.task_status != 'processing' THEN
    RETURN jsonb_build_object(
      'success', true,
      'message', 'task_already_processed',
      'current_status', v_task_record.task_status
    );
  END IF;
  
  -- 生成事务号
  v_trans_no := 'refund_' || extract(epoch from now())::text || '_' || round(random() * 1000000)::text;
  
  -- 更新任务状态为失败
  UPDATE user_generate_tasks 
  SET 
    task_status = 'failed',
    task_result = COALESCE(p_error_info, 'API call failed'),
    completed_at = NOW(),
    updated_at = NOW(),
    version = version + 1  -- 增加版本号
  WHERE id = p_task_id;
  
  -- 返还积分
  INSERT INTO credits (
    trans_no, created_at, user_uuid, trans_type, credits, order_no, expired_at
  ) VALUES (
    v_trans_no,
    NOW(),
    p_user_uuid,
    p_refund_trans_type,
    p_refund_credits,
    NULL,
    NULL
  );
  
  RETURN jsonb_build_object(
    'success', true,
    'message', 'task_failed_and_credits_refunded',
    'refund_trans_no', v_trans_no
  );
  
EXCEPTION
  WHEN OTHERS THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', 'refund_failed',
      'message', SQLERRM
    );
END;
$$ LANGUAGE plpgsql;

 
# Project: Next.js Boilerplate Application

## General

- This is a Next.js TypeScript application with App Router.
- Use React for components development, prefer to use functional components.
- Use Tailwind CSS for styling.
- Use Shadcn UI components.
- Use sonner for toast notifications.
- Use React Context for state management.
- Component names are in CamelCase.
- Use next-auth for authentication.
- Use next-intl for internationalization.
- Use Stripe for payment.

## File Structure

- app/: Next.js App Router pages and API routes
  - [locale]/: Locale-specific pages
  - api/: API routes (e.g. checkout)
  - theme.css: Theme styles
- components/: React components
  - blocks/: Layout blocks (header, footer, etc.), commonly used in Landing Page
  - ui/: Reusable UI components
- contexts/: React contexts (e.g. app context)
- i18n/: Internationalization
  - pages/landing/: Page-specific translations for Landing Page
  - messages/: Global messages
- types/: TypeScript type definitions
  - blocks/: Types for layout blocks
  - pages/: Types for pages
- models/: Data models and data operations.
- services/: Business logics.
- public/: Static assets
- lib/: Custom libraries and functions
- .env.development: Development environment variables

## Coding Conventions

- Use TypeScript for type safety
- Follow React best practices and hooks
- Implement responsive design with Tailwind CSS and Shadcn UI
- Maintain consistent internationalization structure
- Keep components modular and reusable
- Use proper type definitions for components and data

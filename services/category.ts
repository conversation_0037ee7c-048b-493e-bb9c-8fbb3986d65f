import { PromptCategory, UpdatePromptCategoryRequest } from "@/types/prompt-category";

// 更新分类名称
export async function updateCategory(
  categoryUuid: string, 
  updateData: UpdatePromptCategoryRequest
): Promise<PromptCategory> {
  const response = await fetch(`/api/prompt/categories/${categoryUuid}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(updateData),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
  }

  const result = await response.json();
  if (result.code !== 0) {
    throw new Error(result.message || 'Update category failed');
  }

  return result.data;
}

// 删除分类
export async function deleteCategory(categoryUuid: string): Promise<void> {
  const response = await fetch(`/api/prompt/categories/${categoryUuid}`, {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
  }

  const result = await response.json();
  if (result.code !== 0) {
    throw new Error(result.message || 'Delete category failed');
  }
}

// 创建分类
export async function createCategory(name: string): Promise<PromptCategory> {
  const response = await fetch('/api/prompt/categories', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ name }),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
  }

  const result = await response.json();
  if (result.code !== 0) {
    throw new Error(result.message || 'Create category failed');
  }

  return result.data;
} 
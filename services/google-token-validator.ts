export interface GoogleTokenPayload {
  sub: string;
  email: string;
  name: string;
  picture: string;
  email_verified: boolean;
  aud: string;
  iss: string;
  exp: number;
  given_name?: string;
  family_name?: string;
}

export async function validateGoogleToken(
  token: string
): Promise<GoogleTokenPayload | null> {
  try {
    return await validateAccessToken(token);
  } catch (error) {
    console.error('Google token validation failed:', error);
    return null;
  }
}

async function validateAccessToken(token: string): Promise<GoogleTokenPayload | null> {
  try {
    // 使用正确的tokeninfo端点验证access_token
    const response = await fetch(
      `https://www.googleapis.com/oauth2/v3/tokeninfo?access_token=${token}`
    );
    
    if (!response.ok) {
      console.log('Failed to verify access token:', response.status);
      return null;
    }
    
    const tokenInfo = await response.json();

    // 验证aud是否匹配
    if (tokenInfo.aud !== process.env.AUTH_GOOGLE_EXT_ID) {
      console.log('Invalid audience in access token');
      return null;
    }
    
    if (!tokenInfo || !tokenInfo.email) {
      console.log('Invalid token info from access token');
      return null;
    }
    
    // 验证过期时间
    const now = Math.floor(Date.now() / 1000);
    if (tokenInfo.exp && tokenInfo.exp < now) {
      console.log('Access token has expired');
      return null;
    }
    
    // 验证scope，确保包含email
    if (!tokenInfo.scope || !tokenInfo.scope.includes('email')) {
      console.log('Access token does not have email scope');
      return null;
    }

    // 获取用户详细信息
    const userInfoResponse = await fetch(
      'https://www.googleapis.com/oauth2/v2/userinfo',
      {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      }
    );

    if (!userInfoResponse.ok) {
      console.log('Failed to fetch user info:', userInfoResponse.status);
      return null;
    }

    const userInfo = await userInfoResponse.json();
    
    // 合并tokenInfo和userInfo的信息
    return {
      sub: userInfo.id || tokenInfo.sub,
      email: userInfo.email || tokenInfo.email,
      name: userInfo.name || tokenInfo.name || '',
      picture: userInfo.picture || tokenInfo.picture || '',
      email_verified: userInfo.verified_email || tokenInfo.email_verified === 'true',
      aud: tokenInfo.aud || '',
      iss: tokenInfo.iss || '',
      exp: tokenInfo.exp || 0,
      given_name: userInfo.given_name || tokenInfo.given_name,
      family_name: userInfo.family_name || tokenInfo.family_name,
    };
  } catch (error) {
    console.error('Access token validation error:', error);
    return null;
  }
} 
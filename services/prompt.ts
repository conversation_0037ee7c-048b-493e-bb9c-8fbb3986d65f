import { 
  getUserPromptFavorites, 
  getUserPromptFavoritesTotal,
  searchUserPromptFavorites 
} from "@/models/prompt-favorite";
import { getUserPromptCategories } from "@/models/prompt-category";
import { PromptFavoriteWithCategory } from "@/types/prompt-favorite";
import { PromptCategory } from "@/types/prompt-category";

export interface FavoritesWithPagination {
  favorites: PromptFavoriteWithCategory[];
  categories: PromptCategory[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export async function getUserFavoritesWithCategories(
  userUuid: string,
  page: number = 1,
  limit: number = 20,
  categoryUuid?: string,
  keyword?: string
): Promise<FavoritesWithPagination> {
  try {
    let favorites: PromptFavoriteWithCategory[];
    let total: number;

    if (keyword) {
      // 如果有搜索关键词，使用搜索接口
      favorites = await searchUserPromptFavorites(userUuid, keyword, page, limit);
      // 搜索时的总数可能不准确，这里简化处理
      total = favorites.length;
    } else {
      // 正常查询
      favorites = await getUserPromptFavorites(userUuid, categoryUuid, page, limit);
      total = await getUserPromptFavoritesTotal(userUuid, categoryUuid);
    }

    // 获取用户的分类列表
    const categories = await getUserPromptCategories(userUuid);

    return {
      favorites,
      categories,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  } catch (error) {
    console.error("获取用户收藏失败:", error);
    
    // 出错时返回空数据
    return {
      favorites: [],
      categories: [],
      pagination: {
        page: 1,
        limit: 20,
        total: 0,
        pages: 0
      }
    };
  }
} 
import { EnhancedPromptGeneratorConfig } from "@/components/ui/enhanced-prompt-generator";

export const createCommonPromptConfig = (texts: any): EnhancedPromptGeneratorConfig => {
  return {
    texts: {
      title: texts.title,
      subtitle: texts.subtitle,
      inputLabel: texts.inputLabel,
      inputPlaceholder: texts.inputPlaceholder,
      outputLabel: texts.outputLabel,
      outputPlaceholder: texts.outputPlaceholder,
      generateButton: texts.generateButton,
      generating: texts.generating,
      generatingText: texts.generatingText,
      waitingText: texts.waitingText,
      copyButton: texts.copyButton,
      favoriteButton: texts.favoriteButton,
      modeBasic: texts.modeBasic,
      modeAdvanced: texts.modeAdvanced,
      errorNoInput: texts.errorNoInput,
      errorGenerateFailed: texts.errorGenerateFailed,
      errorNoContent: texts.errorNoContent,
      errorCopyFailed: texts.errorCopyFailed,
      errorGetCategoriesFailed: texts.errorGetCategoriesFailed,
      errorGetCategoriesFailedWithMsg: texts.errorGetCategoriesFailedWithMsg,
      errorFavoriteFailed: texts.errorFavoriteFailed,
      successGenerated: texts.successGenerated,
      successCopied: texts.successCopied,
      successFavoriteSaved: texts.successFavoriteSaved,
    },
    features: {
      enableModeSwitch: true,
      enableFavorite: true,
      enableCopy: true,
    },
    request: {
      modelType: texts.modelType,
      modelName: texts.modelName,
    },
  };
};

// 可以添加其他配置，比如：
// export const createGeminiPromptConfig = (texts: any): EnhancedPromptGeneratorConfig => { ... }
// export const createClaudePromptConfig = (texts: any): EnhancedPromptGeneratorConfig => { ... } 
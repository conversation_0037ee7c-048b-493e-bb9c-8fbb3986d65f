"use client";

import { useState, useEffect } from "react";
import { useTranslations } from "next-intl";
import { useSearchParams, useRouter, usePathname } from "next/navigation";
import { FavoritesSlotProps } from "@/types/slots/favorites";
import CategoryNav from "@/components/console/slots/favorites/category-nav";
import PromptCard from "@/components/console/slots/favorites/prompt-card";
import Toolbar from "./toolbar";
import Empty from "@/components/blocks/empty";
import { toast } from "sonner";
import { updateCategory } from "@/services/category";
import { PromptCategory } from "@/types/prompt-category";
import FavoriteModal from "@/components/ui/favorite-modal";
import DeleteConfirmDialog from "./delete-confirm-dialog";
import Pagination from "@/components/ui/pagination";
import { Button } from "@/components/ui/button";

export default function FavoritesSlot({
  favorites,
  categories,
  pagination,
  selectedCategory,
  searchKeyword,
}: FavoritesSlotProps) {
  const t = useTranslations();
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  
  const [searchValue, setSearchValue] = useState(searchKeyword || "");
  const [showAddFavoriteModal, setShowAddFavoriteModal] = useState(false);
  const [showEditFavoriteModal, setShowEditFavoriteModal] = useState(false);
  const [editingFavorite, setEditingFavorite] = useState<any>(null);
  const [deleteDialog, setDeleteDialog] = useState({
    open: false,
    favoriteUuid: "",
    favoriteTitle: "",
  });
  const [isDeleting, setIsDeleting] = useState(false);
  const [isNavigating, setIsNavigating] = useState(false);
  const [hasError, setHasError] = useState(false);

  // 当页面数据变化时重置导航状态和错误状态
  useEffect(() => {
    setIsNavigating(false);
    setHasError(false);
  }, [pagination.page, favorites.length]);

  // 错误边界处理：检测无效页码
  useEffect(() => {
    // 如果当前页码大于总页数且总页数不为0，重定向到第一页
    if (pagination.page > pagination.pages && pagination.pages > 0) {
      toast.error(t("my_favorites.pagination.invalid_page"));
      const params = new URLSearchParams(searchParams);
      params.set("page", "1");
      const queryString = params.toString();
      const localePath = getLocalePath();
      router.replace(`${localePath}/my-favorites${queryString ? `?${queryString}` : ''}`);
    }
  }, [pagination.page, pagination.pages, searchParams, router, t]);

  // 获取当前语言路径前缀
  const getLocalePath = () => {
    const pathSegments = pathname.split('/');
    if (pathSegments[1] && ['zh'].includes(pathSegments[1])) {
      return `/${pathSegments[1]}`;
    }
    return '';
  };

  // 处理分类切换
  const handleCategoryChange = (categoryId?: string) => {
    const params = new URLSearchParams(searchParams);
    if (categoryId) {
      params.set("category", categoryId);
    } else {
      params.delete("category");
    }
    params.delete("page"); // 重置页码
    const queryString = params.toString();
    const localePath = getLocalePath();
    router.push(`${localePath}/my-favorites${queryString ? `?${queryString}` : ''}`);
  };

  // 处理搜索
  const handleSearchChange = (value: string) => {
    setSearchValue(value);
    const params = new URLSearchParams(searchParams);
    if (value) {
      params.set("keyword", value);
    } else {
      params.delete("keyword");
    }
    params.delete("page"); // 重置页码
    const queryString = params.toString();
    const localePath = getLocalePath();
    router.push(`${localePath}/my-favorites${queryString ? `?${queryString}` : ''}`);
  };

  // 处理页码切换
  const handlePageChange = (newPage: number) => {
    if (newPage === pagination.page || isNavigating) return;
    
    // 边界检查
    if (newPage < 1 || newPage > pagination.pages) {
      toast.error(t("my_favorites.pagination.invalid_page"));
      return;
    }
    
    setIsNavigating(true);
    
    try {
      const params = new URLSearchParams(searchParams);
      params.set("page", newPage.toString());
      
      const queryString = params.toString();
      const localePath = getLocalePath();
      router.push(`${localePath}/my-favorites${queryString ? `?${queryString}` : ''}`);
    } catch (error) {
      console.error("Navigation error:", error);
      toast.error(t("my_favorites.pagination.error"));
      setIsNavigating(false);
      setHasError(true);
    }
  };

  // 处理重试
  const handleRetry = () => {
    setHasError(false);
    router.refresh();
  };

  // 处理复制
  const handleCopy = async (content: string) => {
    try {
      await navigator.clipboard.writeText(content);
      toast.success(t("my_favorites.card.copy_success"));
    } catch (error) {
      toast.error("Copy failed");
    }
  };

  // 处理删除 - 显示确认对话框
  const handleDelete = async (id: string) => {
    const favorite = favorites.find(f => f.uuid === id);
    setDeleteDialog({
      open: true,
      favoriteUuid: id,
      favoriteTitle: favorite?.title || "",
    });
  };

  // 确认删除
  const handleDeleteConfirm = async () => {
    try {
      setIsDeleting(true);
      
      const response = await fetch(`/api/prompt/favorites/${deleteDialog.favoriteUuid}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const result = await response.json();
      
      if (result.code === 0) {
        toast.success(t("my_favorites.card.delete_success"));
        // 关闭对话框
        setDeleteDialog({ open: false, favoriteUuid: "", favoriteTitle: "" });
        // 刷新页面数据
        router.refresh();
      } else {
        toast.error(result.message || t("my_favorites.card.delete_failed"));
      }
    } catch (error) {
      console.error("Delete favorite failed:", error);
      toast.error(t("my_favorites.card.delete_failed"));
    } finally {
      setIsDeleting(false);
    }
  };

  // 处理编辑
  const handleEdit = (favorite: any) => {
    setEditingFavorite(favorite);
    setShowEditFavoriteModal(true);
  };

  // 处理查看详情
  const handleView = (favorite: any) => {
    // TODO: 实现查看详情功能
    console.log("View favorite:", favorite);
  };

  // 处理新增收藏
  const handleAddNew = () => {
    setShowAddFavoriteModal(true);
  };

  // 处理新增分类
  const handleAddCategory = () => {
    // TODO: 实现新增分类功能
    console.log("Add new category");
  };

  // 创建新分类
  const createNewCategory = async (name: string) => {
    try {
      const response = await fetch('/api/prompt/categories', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name: name.trim() }),
      });

      const data = await response.json();
      if (data.code === 0) {
        return data.data.uuid;
      } else {
        // 根据错误类型显示不同的提示
        if (data.message.includes("already exists")) {
          toast.error(t("prompt_generator.category_already_exists"));
          throw new Error(t("prompt_generator.category_already_exists"));
        } else {
          throw new Error(data.message || t("prompt_generator.error_favorite_failed"));
        }
      }
    } catch (error) {
      throw error;
    }
  };

  // 处理新增收藏保存
  const handleAddFavoriteSave = async (data: {
    title: string;
    content: string;
    notes: string;
    categoryUuid: string | null;
    isNewCategory: boolean;
    newCategoryName: string;
  }) => {
    try {
      let categoryUuid = data.categoryUuid;

      // 如果选择新建分类且还没有分类UUID，先创建分类
      // 注意：这里添加了 !categoryUuid 的条件，避免重复创建
      if (data.isNewCategory && data.newCategoryName && !categoryUuid) {
        categoryUuid = await createNewCategory(data.newCategoryName);
      }

      const response = await fetch('/api/prompt/favorites', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: data.title,
          prompt: data.content,
          category_uuid: categoryUuid || "",
          notes: data.notes || "",
        }),
      });

      const result = await response.json();
      if (result.code === 0) {
        toast.success(t("prompt_generator.success_favorite_saved"));
        // 刷新页面以显示新创建的收藏
        router.refresh();
      } else {
        toast.error(result.message || t("prompt_generator.error_favorite_failed"));
        throw new Error(result.message || t("prompt_generator.error_favorite_failed"));
      }
    } catch (error) {
      toast.error(error instanceof Error ? error.message : t("prompt_generator.error_favorite_failed"));
      throw error;
    }
  };

  // 处理编辑收藏保存
  const handleEditFavoriteSave = async (data: {
    title: string;
    content: string;
    notes: string;
    categoryUuid: string | null;
    isNewCategory: boolean;
    newCategoryName: string;
  }) => {
    try {
      let categoryUuid = data.categoryUuid;

      // 如果选择新建分类，先创建分类
      if (data.isNewCategory && data.newCategoryName && !categoryUuid) {
        categoryUuid = await createNewCategory(data.newCategoryName);
      }

      const response = await fetch(`/api/prompt/favorites/${editingFavorite.uuid}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: data.title,
          prompt: data.content,
          category_uuid: categoryUuid || "",
          notes: data.notes || "",
        }),
      });

      const result = await response.json();
      if (result.code === 0) {
        toast.success(t("my_favorites.card.edit_success"));
        // 刷新页面以显示更新后的收藏
        router.refresh();
      } else {
        toast.error(result.message || t("my_favorites.card.edit_failed"));
        throw new Error(result.message || t("my_favorites.card.edit_failed"));
      }
    } catch (error) {
      toast.error(error instanceof Error ? error.message : t("my_favorites.card.edit_failed"));
      throw error;
    }
  };

  // 处理编辑分类
  const handleEditCategory = async (category: PromptCategory, newName: string) => {
    try {
      await updateCategory(category.uuid, { name: newName });
      // 刷新页面以获取最新数据
      router.refresh();
    } catch (error) {
      // 错误处理在 EditCategoryDialog 组件中进行
      throw error;
    }
  };

  // 处理删除分类
  const handleDeleteCategory = () => {
    // 刷新页面以获取最新数据
    router.refresh();
  };

  // 计算选中分类的收藏数量
  const getSelectedCategoryCount = () => {
    if (!selectedCategory) return favorites.length;
    return favorites.filter(f => f.category_uuid === selectedCategory).length;
  };

  return (
    <div className="space-y-6">
      {/* 工具栏 - 移到顶部 */}
      <Toolbar
        searchValue={searchValue}
        onSearchChange={handleSearchChange}
        onAddNew={handleAddNew}
        totalCount={getSelectedCategoryCount()}
      />

      <div className="flex flex-col lg:flex-row gap-6">
        {/* 左侧分类导航 - 在大屏上显示为侧边栏，小屏上显示为水平滚动 */}
        <div className="lg:w-64 lg:flex-shrink-0">
          <CategoryNav
            categories={categories}
            selectedCategory={selectedCategory}
            onCategoryChange={handleCategoryChange}
            onAddCategory={handleAddCategory}
            onEditCategory={handleEditCategory}
            onDeleteCategory={handleDeleteCategory}
            totalCount={favorites.length}
          />
        </div>

        {/* 右侧主内容区 */}
        <div className="flex-1 min-w-0">
          {/* 收藏卡片网格 */}
          {favorites.length === 0 ? (
            <Empty message={t("my_favorites.empty_message")} />
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-5 lg:gap-6">
              {favorites.map((favorite) => (
                <PromptCard
                  key={favorite.uuid}
                  favorite={favorite}
                  onCopy={handleCopy}
                  onDelete={handleDelete}
                  onEdit={handleEdit}
                  onView={handleView}
                />
              ))}
            </div>
          )}

          {/* 分页组件 */}
          <div className="mt-8 flex justify-center">
            {hasError ? (
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <span>{t("my_favorites.pagination.error")}</span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRetry}
                  className="h-8 px-3"
                >
                  {t("my_favorites.pagination.retry")}
                </Button>
              </div>
            ) : (
              <Pagination
                currentPage={pagination.page}
                totalPages={pagination.pages}
                totalItems={pagination.total}
                pageSize={pagination.limit}
                onPageChange={handlePageChange}
                isLoading={isNavigating}
              />
            )}
          </div>
        </div>
      </div>

      {/* 新增收藏模态框 */}
      <FavoriteModal
        open={showAddFavoriteModal}
        onOpenChange={setShowAddFavoriteModal}
        mode="create"
        categories={categories}
        onSave={handleAddFavoriteSave}
        onCreateCategory={createNewCategory}
      />

      {/* 编辑收藏模态框 */}
      <FavoriteModal
        open={showEditFavoriteModal}
        onOpenChange={setShowEditFavoriteModal}
        mode="edit"
        initialData={editingFavorite ? {
          uuid: editingFavorite.uuid,
          title: editingFavorite.title,
          content: editingFavorite.prompt,
          notes: editingFavorite.notes,
          categoryUuid: editingFavorite.category_uuid,
        } : undefined}
        categories={categories}
        onSave={handleEditFavoriteSave}
        onCreateCategory={createNewCategory}
      />

      {/* 删除确认对话框 */}
      <DeleteConfirmDialog
        open={deleteDialog.open}
        onOpenChange={(open) => setDeleteDialog(prev => ({ ...prev, open }))}
        isLoading={isDeleting}
        itemTitle={deleteDialog.favoriteTitle}
        onConfirm={handleDeleteConfirm}
      />
    </div>
  );
} 
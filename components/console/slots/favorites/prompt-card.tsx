"use client";

import { useTranslations } from "next-intl";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>eader } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Icon from "@/components/icon";
import { PromptCardProps } from "@/types/slots/favorites";
import { cn } from "@/lib/utils";
import moment from "moment";

export default function PromptCard({
  favorite,
  onCopy,
  onDelete,
  onEdit,
  onView,
}: PromptCardProps) {
  const t = useTranslations();

  // 截断 Prompt 内容用于预览
  const truncatePrompt = (text: string, maxLength: number = 180) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + "...";
  };

  // 获取模型标签颜色
  const getModelBadgeVariant = (modelName?: string) => {
    if (!modelName) return "secondary";
    
    if (modelName.includes("gpt-4")) return "default";
    if (modelName.includes("gpt-3")) return "secondary";
    if (modelName.includes("claude")) return "outline";
    return "secondary";
  };

  return (
    <Card className="group hover:shadow-lg transition-all duration-200 hover:scale-[1.02] flex flex-col h-[300px] overflow-hidden">
      <CardHeader className="flex-shrink-0">
        <div className="flex items-start justify-between gap-2">
          <div className="flex-1 min-w-0">
            {/* 标题 */}
            <h3 className="font-semibold text-base line-clamp-2 mb-2">
              {favorite.title || "未命名 Prompt"}
            </h3>
          </div>
          
          {/* 右上角操作菜单 */}
          <div className="transition-opacity flex-shrink-0">
            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="sm"
                className="h-7 w-7 p-0"
                onClick={() => onCopy(favorite.prompt)}
                title={t("my_favorites.card.copy")}
              >
                <Icon name="RiFileCopyLine" className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="h-7 w-7 p-0"
                onClick={() => onEdit(favorite)}
                title={t("my_favorites.card.edit")}
              >
                <Icon name="RiEditLine" className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="h-7 w-7 p-0 text-destructive hover:bg-destructive/10"
                onClick={() => onDelete(favorite.uuid)}
                title={t("my_favorites.card.delete")}
              >
                <Icon name="RiDeleteBinLine" className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="flex-1 flex flex-col min-h-0">
        {/* Prompt 内容预览 */}
        <div 
          className={cn(
            "text-sm text-muted-foreground cursor-pointer hover:text-foreground transition-colors overflow-hidden",
            favorite.notes ? "flex-shrink min-h-0" : "flex-1"
          )}
          onClick={() => onView(favorite)}
          title="点击查看完整内容"
        >
          <div className={cn(
            "leading-relaxed",
            favorite.notes ? "line-clamp-3" : "line-clamp-4"
          )}>
            "{truncatePrompt(favorite.prompt)}"
          </div>
        </div>
        
        {/* 备注（如果有） */}
        {favorite.notes && (
          <div className="mt-2 p-2 bg-muted/30 rounded text-xs text-muted-foreground flex-shrink-0">
            <div className="flex items-start gap-1">
              <Icon name="RiStickyNoteLine" className="h-3 w-3 flex-shrink-0 mt-0.5" />
              <span className="line-clamp-2">{favorite.notes}</span>
            </div>
          </div>
        )}
      </CardContent>

      <CardFooter className="pt-2 border-t flex-shrink-0">
        <div className="flex items-center justify-between w-full text-xs text-muted-foreground">
          {/* 创建时间 */}
          <div className="flex items-center gap-1 flex-shrink-0">
            <Icon name="RiTimeLine" className="h-3 w-3" />
            <span>{moment(favorite.created_at).format("MM-DD HH:mm")}</span>
          </div>
          
          {/* 分类和模型标签 */}
          <div className="flex items-center gap-2 flex-wrap">
            {favorite.category_name && (
              <Badge variant="outline" className="text-xs flex items-center gap-1">
                <Icon name="RiFolderLine" className="h-3 w-3" />
                <span className="truncate max-w-[80px]">{favorite.category_name}</span>
              </Badge>
            )}
            {favorite.model_name && (
              <Badge variant={getModelBadgeVariant(favorite.model_name)} className="text-xs flex items-center gap-1">
                <Icon name="RiRobotLine" className="h-3 w-3" />
                <span className="truncate max-w-[100px]">{favorite.model_name}</span>
              </Badge>
            )}
          </div>
        </div>
      </CardFooter>
    </Card>
  );
} 
"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import { toast } from "sonner";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Icon from "@/components/icon";
import { CategoryNavProps } from "@/types/slots/favorites";
import { PromptCategory } from "@/types/prompt-category";
import { cn } from "@/lib/utils";
import EditCategoryDialog from "./edit-category-dialog";
import DeleteConfirmDialog from "./delete-confirm-dialog";

export default function CategoryNav({
  categories,
  selectedCategory,
  onCategoryChange,
  onAddCategory,
  onEditCategory,
  onDeleteCategory,
  totalCount,
}: CategoryNavProps) {
  const t = useTranslations();
  const [editingCategory, setEditingCategory] = useState<PromptCategory | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [deleteCategoryDialog, setDeleteCategoryDialog] = useState({
    open: false,
    categoryUuid: "",
    categoryName: "",
  });
  const [isDeletingCategory, setIsDeletingCategory] = useState(false);

  // 计算每个分类的收藏数量
  const getCategoryCount = (categoryUuid: string) => {
    // TODO: 这里应该从后端获取真实的分类统计数据
    // 目前简化处理，返回固定值
    // 后续可以在 API 中添加分类统计功能
    return 0;
  };

  const allCount = totalCount;

  // 处理编辑分类
  const handleEditCategory = (category: PromptCategory) => {
    setEditingCategory(category);
    setIsEditDialogOpen(true);
  };

  // 处理保存编辑
  const handleSaveEdit = async (category: PromptCategory, newName: string) => {
    await onEditCategory(category, newName);
  };

  // 关闭编辑弹窗
  const handleCloseEditDialog = () => {
    setIsEditDialogOpen(false);
    setEditingCategory(null);
  };

  // 处理删除分类按钮点击
  const handleDeleteCategory = async (category: PromptCategory) => {
    try {
      // 先检查是否有prompt使用该分类
      const response = await fetch(`/api/prompt/categories/${category.uuid}/check-usage`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const result = await response.json();
      
      if (result.code !== 0) {
        toast.error(result.message || t("my_favorites.categories.delete_failed"));
        return;
      }

      // 如果有使用该分类的prompt，显示错误提示
      if (result.data.hasUsage) {
        toast.error(t("my_favorites.categories.delete_failed_has_usage"));
        return;
      }

      // 没有关联的prompt，显示确认对话框
      setDeleteCategoryDialog({
        open: true,
        categoryUuid: category.uuid,
        categoryName: category.name,
      });
    } catch (error) {
      console.error("Check category usage failed:", error);
      toast.error(t("my_favorites.categories.delete_failed"));
    }
  };

  // 确认删除分类
  const handleDeleteCategoryConfirm = async () => {
    try {
      setIsDeletingCategory(true);
      
      const response = await fetch(`/api/prompt/categories/${deleteCategoryDialog.categoryUuid}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const result = await response.json();
      
      if (result.code === 0) {
        toast.success(t("my_favorites.categories.delete_success"));
        // 关闭对话框
        setDeleteCategoryDialog({ open: false, categoryUuid: "", categoryName: "" });
        // 刷新页面数据 - 通过回调通知父组件
        onDeleteCategory?.();
      } else {
        toast.error(result.message || t("my_favorites.categories.delete_failed"));
      }
    } catch (error) {
      console.error("Delete category failed:", error);
      toast.error(t("my_favorites.categories.delete_failed"));
    } finally {
      setIsDeletingCategory(false);
    }
  };

  return (
    <Card className="h-fit">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between text-base">
          <span className="flex items-center gap-2">
            <Icon name="RiFolderLine" className="h-4 w-4" />
            {t("my_favorites.categories.title")}
          </span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-1">
        {/* 全部分类 */}
        <div
          className={cn(
            "flex items-center justify-between p-2.5 rounded-lg cursor-pointer transition-colors text-sm",
            !selectedCategory
              ? "bg-primary/10 text-primary"
              : "hover:bg-muted/50"
          )}
          onClick={() => onCategoryChange()}
        >
          <div className="flex items-center gap-2 flex-1 min-w-0">
            <Icon name="RiFolder3Line" className="h-4 w-4 flex-shrink-0" />
            <span className="font-medium truncate">{t("my_favorites.categories.all")}</span>
          </div>
          {/* <Badge variant="secondary" className="text-xs ml-2 flex-shrink-0">
            {allCount}
          </Badge> */}
        </div>

        {/* 分类列表 */}
        {categories.map((category) => {
          const count = getCategoryCount(category.uuid);
          const isSelected = selectedCategory === category.uuid;
          
          return (
            <div
              key={category.uuid}
              className={cn(
                "flex items-center justify-between p-2.5 rounded-lg cursor-pointer transition-colors group text-sm",
                isSelected
                  ? "bg-primary/10 text-primary"
                  : "hover:bg-muted/50"
              )}
              onClick={() => onCategoryChange(category.uuid)}
            >
              <div className="flex items-center gap-2 flex-1 min-w-0">
                <Icon name="RiFolderLine" className="h-4 w-4 flex-shrink-0" />
                <span className="font-medium truncate">{category.name}</span>
              </div>
              <div className="flex items-center gap-2 flex-shrink-0">
                {/* <Badge variant="secondary" className="text-xs">
                  {count}
                </Badge> */}
                {/* 编辑和删除按钮 - 悬停时显示 */}
                <div className="hidden group-hover:flex items-center gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-5 w-5 p-0"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleEditCategory(category);
                    }}
                    title={t("my_favorites.categories.edit_category")}
                  >
                    <Icon name="RiEditLine" className="h-3 w-3" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-5 w-5 p-0 text-destructive hover:bg-destructive/10"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteCategory(category);
                    }}
                  >
                    <Icon name="RiDeleteBinLine" className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            </div>
          );
        })}
      </CardContent>
      
      {/* 编辑分类弹窗 */}
      <EditCategoryDialog
        category={editingCategory}
        isOpen={isEditDialogOpen}
        onClose={handleCloseEditDialog}
        onSave={handleSaveEdit}
      />
      
      {/* 删除分类确认对话框 */}
      <DeleteConfirmDialog
        open={deleteCategoryDialog.open}
        onOpenChange={(open) => setDeleteCategoryDialog(prev => ({ ...prev, open }))}
        type="category"
        isLoading={isDeletingCategory}
        itemTitle={deleteCategoryDialog.categoryName}
        onConfirm={handleDeleteCategoryConfirm}
      />
    </Card>
  );
} 
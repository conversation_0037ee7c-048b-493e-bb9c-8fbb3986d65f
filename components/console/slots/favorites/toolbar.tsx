"use client";

import { useTranslations } from "next-intl";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import Icon from "@/components/icon";
import { ToolbarProps } from "@/types/slots/favorites";
import { useDebouncedCallback } from "use-debounce";

export default function Toolbar({
  searchValue,
  onSearchChange,
  onAddNew,
  totalCount,
}: ToolbarProps) {
  const t = useTranslations();

  // 防抖搜索
  const debouncedSearch = useDebouncedCallback((value: string) => {
    onSearchChange(value);
  }, 300);

  return (
    <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 mb-6">
      {/* 左侧：标题和统计 */}
      <div className="flex flex-col gap-1">
        <h1 className="text-2xl font-semibold">{t("my_favorites.title")}</h1>
        <p className="text-sm text-muted-foreground">
          {t("my_favorites.total_count", { count: totalCount })}
        </p>
      </div>

      {/* 右侧：搜索和操作 */}
      <div className="flex items-center gap-3 w-full sm:w-auto">
        {/* 搜索框 */}
        {/* <div className="relative flex-1 sm:w-64">
          <Icon 
            name="RiSearchLine" 
            className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" 
          />
          <Input
            placeholder={t("my_favorites.search_placeholder")}
            defaultValue={searchValue}
            onChange={(e) => debouncedSearch(e.target.value)}
            className="pl-10"
          />
        </div> */}

        {/* 新增收藏按钮 */}
        <Button onClick={onAddNew} className="flex-shrink-0">
          <Icon name="RiAddLine" className="h-4 w-4 mr-2" />
          {t("my_favorites.add_new")}
        </Button>
      </div>
    </div>
  );
} 
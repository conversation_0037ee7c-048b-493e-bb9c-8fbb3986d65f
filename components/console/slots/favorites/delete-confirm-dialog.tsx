"use client";

import { useTranslations } from "next-intl";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Loader2, AlertTriangle } from "lucide-react";

interface DeleteConfirmDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  isLoading: boolean;
  type?: 'favorite' | 'category';
  itemTitle: string;
  onConfirm: () => Promise<void>;
}

export default function DeleteConfirmDialog({
  open,
  onOpenChange,
  isLoading,
  type = 'favorite',
  itemTitle,
  onConfirm,
}: DeleteConfirmDialogProps) {
  const t = useTranslations();

  // 根据类型选择不同的国际化键
  const dialogKeys = type === 'category' 
    ? 'my_favorites.delete_category_dialog'
    : 'my_favorites.delete_dialog';

  const handleConfirm = async () => {
    try {
      await onConfirm();
    } catch (error) {
      // 错误处理在父组件中进行
      console.error("Delete confirm error:", error);
    }
  };

  const handleClose = () => {
    if (isLoading) return; // 删除中不允许关闭
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-destructive" />
            {t(`${dialogKeys}.title`)}
          </DialogTitle>
          <DialogDescription>
            {t(`${dialogKeys}.description`)}
            {itemTitle && (
              <span className="font-medium text-foreground">
                "{itemTitle}"
              </span>
            )}
            ？{t(`${dialogKeys}.warning`)}
          </DialogDescription>
        </DialogHeader>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={isLoading}
          >
            {t(`${dialogKeys}.cancel_button`)}
          </Button>
          <Button
            type="button"
            variant="destructive"
            onClick={handleConfirm}
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {t(`${dialogKeys}.deleting`)}
              </>
            ) : (
              t(`${dialogKeys}.confirm_button`)
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 
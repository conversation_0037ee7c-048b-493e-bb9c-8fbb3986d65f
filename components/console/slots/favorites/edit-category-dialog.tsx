"use client";

import { useState, useEffect } from "react";
import { useTranslations } from "next-intl";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { PromptCategory } from "@/types/prompt-category";

interface EditCategoryDialogProps {
  category: PromptCategory | null;
  isOpen: boolean;
  onClose: () => void;
  onSave: (category: PromptCategory, newName: string) => Promise<void>;
}

export default function EditCategoryDialog({
  category,
  isOpen,
  onClose,
  onSave,
}: EditCategoryDialogProps) {
  const t = useTranslations();
  const [name, setName] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");

  // 当分类变化时，重置表单
  useEffect(() => {
    if (category) {
      setName(category.name);
      setError("");
    }
  }, [category]);

  // 验证分类名称
  const validateName = (value: string): string => {
    const trimmedValue = value.trim();
    if (!trimmedValue) {
      return t("my_favorites.categories.edit_dialog.name_required");
    }
    if (trimmedValue.length > 50) {
      return t("my_favorites.categories.edit_dialog.name_too_long");
    }
    return "";
  };

  // 处理输入变化
  const handleNameChange = (value: string) => {
    setName(value);
    if (error) {
      const validationError = validateName(value);
      setError(validationError);
    }
  };

  // 处理保存
  const handleSave = async () => {
    if (!category) return;

    const trimmedName = name.trim();
    const validationError = validateName(trimmedName);
    
    if (validationError) {
      setError(validationError);
      return;
    }

    // 如果名称没有变化，直接关闭
    if (trimmedName === category.name) {
      onClose();
      return;
    }

    setIsLoading(true);
    setError("");

    try {
      await onSave(category, trimmedName);
      toast.success(t("my_favorites.categories.edit_dialog.update_success"));
      onClose();
    } catch (error) {
      console.error("Failed to update category:", error);
      const errorMessage = error instanceof Error 
        ? error.message 
        : t("my_favorites.categories.edit_dialog.update_failed");
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !isLoading) {
      e.preventDefault();
      handleSave();
    }
  };

  // 处理弹窗关闭
  const handleClose = () => {
    if (isLoading) return; // 加载中不允许关闭
    setError("");
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            {t("my_favorites.categories.edit_dialog.title")}
          </DialogTitle>
          <DialogDescription>
            {t("my_favorites.categories.edit_dialog.description")}
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="category-name" className="text-right">
              {t("my_favorites.categories.edit_dialog.name_label")}
            </Label>
            <div className="col-span-3 space-y-2">
              <Input
                id="category-name"
                value={name}
                onChange={(e) => handleNameChange(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder={t("my_favorites.categories.edit_dialog.name_placeholder")}
                disabled={isLoading}
                className={error ? "border-destructive" : ""}
              />
              {error && (
                <p className="text-sm text-destructive">{error}</p>
              )}
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={isLoading}
          >
            {t("my_favorites.categories.edit_dialog.cancel_button")}
          </Button>
          <Button
            type="button"
            onClick={handleSave}
            disabled={isLoading || !!validateName(name)}
          >
            {isLoading ? (
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                {t("my_favorites.categories.edit_dialog.saving")}
              </div>
            ) : (
              t("my_favorites.categories.edit_dialog.save_button")
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 
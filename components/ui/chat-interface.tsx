"use client";

import { useState, useRef, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Card } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Send, Bot, User, Settings } from "lucide-react";
import { toast } from "sonner";
import { useTranslations } from "next-intl";
import { useAppContext } from "@/contexts/app";

interface Message {
  id: string;
  role: "user" | "assistant" | "system";
  content: string;
  timestamp: Date;
}

export interface ChatModel {
  id: string;
  name: string;
  provider: string;
}

export interface ChatInterfaceConfig {
  title: string;
  subtitle?: string;
  systemPrompt: string;
  models: ChatModel[];
  defaultModel?: string;
}

interface ChatInterfaceProps {
  config: ChatInterfaceConfig;
  className?: string;
}

export default function ChatInterface({ 
  config, 
  className = "" 
}: ChatInterfaceProps) {
  const t = useTranslations("chat_interface");
  const { user, setShowSignModal } = useAppContext();
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [selectedModel, setSelectedModel] = useState(config.defaultModel || config.models[0]?.id);
  const [shouldAutoScroll, setShouldAutoScroll] = useState(true);
  const messagesContainerRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    if (shouldAutoScroll && messagesContainerRef.current) {
      messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
    }
  };

  // 检查用户是否在消息容器内手动滚动
  const handleMessagesScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const element = e.currentTarget;
    const isNearBottom = element.scrollHeight - element.scrollTop <= element.clientHeight + 100;
    setShouldAutoScroll(isNearBottom);
  };

  useEffect(() => {
    // 只在新消息添加时自动滚动
    if (messages.length > 0) {
      const lastMessage = messages[messages.length - 1];
      // 如果是新的用户消息或者AI开始回复，则滚动到底部
      if (lastMessage.role === "user" || (lastMessage.role === "assistant" && lastMessage.content === "")) {
        setShouldAutoScroll(true);
        scrollToBottom();
      }
    }
  }, [messages.length]);

  // 当生成完成时，确保滚动到底部
  useEffect(() => {
    if (!isGenerating && shouldAutoScroll) {
      setTimeout(() => scrollToBottom(), 100);
    }
  }, [isGenerating]);

  const handleModelChange = (modelId: string) => {
    const selectedModelData = config.models.find(m => m.id === modelId);
    
    // 检查是否为付费模型（名称包含Credit的为付费模型）
    const isPaidModel = selectedModelData?.name.includes("Credit");
    
    if (isPaidModel && !user) {
      setShowSignModal(true);
      return;
    }
    
    setSelectedModel(modelId);
  };

  const sendMessage = async () => {
    if (!input.trim() || isGenerating) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      role: "user",
      content: input.trim(),
      timestamp: new Date(),
    };

    // 立即添加用户消息和"正在思考"的助手消息
    const thinkingMessage: Message = {
      id: (Date.now() + 1).toString(),
      role: "assistant",
      content: "",
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage, thinkingMessage]);
    const currentInput = input.trim();
    setInput("");
    setIsGenerating(true);

    try {
      const selectedModelData = config.models.find(m => m.id === selectedModel);
      const response = await fetch("/api/chat/stream", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          messages: [
            { role: "system", content: config.systemPrompt },
            ...messages.map(m => ({ role: m.role, content: m.content })),
            { role: "user", content: currentInput }
          ],
          model: selectedModel,
          provider: selectedModelData?.provider || "openrouter",
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Response error:", response.status, errorText);
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      // 使用 readableStreamDefaultReader 处理流式响应
      const reader = response.body?.getReader();
      if (!reader) throw new Error("No reader available");

      const decoder = new TextDecoder();

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value, { stream: true });
          const lines = chunk.split('\n');

          for (const line of lines) {
            if (line.trim() === '') continue;
            
            console.log("Raw line:", line);
            
            // 处理 AI SDK 数据流格式 - 直接字符串格式
            if (line.startsWith('0:')) {
              try {
                const textContent = line.slice(2); // 移除 "0:" 前缀
                const parsed = JSON.parse(textContent); // 这是直接的字符串内容
                console.log("Parsed chunk:", parsed);
                
                // 直接使用解析出的字符串内容
                if (typeof parsed === 'string') {
                  setMessages(prev => prev.map(msg => 
                    msg.id === thinkingMessage.id 
                      ? { ...msg, content: msg.content + parsed }
                      : msg
                  ));
                }
              } catch (e) {
                console.error('JSON parse error:', e, 'Line:', line);
              }
            }
            // 忽略其他类型的行（如 f:, e:, d: 等元数据）
          }
        }
      } finally {
        reader.releaseLock();
      }
    } catch (error) {
      console.error("Chat error:", error);
      toast.error("Failed to get response: " + (error as Error).message);
      setMessages(prev => prev.slice(0, -1)); // 移除思考中的消息
    } finally {
      setIsGenerating(false);
    }
  };

  const clearChat = () => {
    setMessages([]);
  };

  return (
    <div className={`w-full max-w-4xl mx-auto ${className}`}>
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold mb-2">{config.title}</h2>
        {config.subtitle && (
          <p className="text-gray-600">{config.subtitle}</p>
        )}
      </div>

      <Card className="h-[600px] flex flex-col">
        {/* Header with model selection */}
        <div className="p-4 border-b flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              <span className="text-sm font-medium">{t("model")}</span>
            </div>
            <Select value={selectedModel} onValueChange={handleModelChange}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {config.models.map((model) => (
                  <SelectItem key={model.id} value={model.id}>
                    {model.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <Button variant="outline" size="sm" onClick={clearChat}>
            {t("clear")}
          </Button>
        </div>

        {/* Messages area */}
        <div 
          ref={messagesContainerRef}
          className="flex-1 overflow-y-auto p-4 space-y-4"
          onScroll={handleMessagesScroll}
        >
          {messages.length === 0 && (
            <div className="text-center text-gray-500 mt-8">
              <Bot className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>{t("start_conversation")}</p>
            </div>
          )}
          
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex gap-3 ${
                message.role === "user" ? "justify-end" : "justify-start"
              }`}
            >
              {message.role === "assistant" && (
                <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0">
                  <Bot className="h-4 w-4 text-blue-600" />
                </div>
              )}
              
              <div
                className={`max-w-[80%] rounded-lg p-3 ${
                  message.role === "user"
                    ? "bg-blue-600 text-white"
                    : "bg-gray-100 text-gray-900"
                }`}
              >
                {message.role === "assistant" && message.content === "" && isGenerating ? (
                  <div className="flex items-center gap-2 text-gray-500">
                    <div className="flex gap-1">
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
                    </div>
                    <span className="text-sm">{t("thinking")}</span>
                  </div>
                ) : (
                  <div className="whitespace-pre-wrap">{message.content}</div>
                )}
                <div className={`text-xs mt-1 opacity-70`}>
                  {message.timestamp.toLocaleTimeString()}
                </div>
              </div>

              {message.role === "user" && (
                <div className="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center flex-shrink-0">
                  <User className="h-4 w-4 text-white" />
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Input area */}
        <div className="p-4 border-t">
          <div className="relative">
            <Textarea
              value={input}
              onChange={(e) => setInput(e.target.value)}
              placeholder={t("type_message")}
              className="flex-1 min-h-[60px] resize-none pr-12 pb-12"
              onKeyDown={(e) => {
                if (e.key === "Enter" && !e.shiftKey) {
                  e.preventDefault();
                  sendMessage();
                }
              }}
            />
            <Button
              onClick={sendMessage}
              disabled={!input.trim() || isGenerating}
              className="absolute bottom-2 right-2 h-8 w-8 p-0"
              size="sm"
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
}

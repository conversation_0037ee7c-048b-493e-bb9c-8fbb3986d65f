"use client";

import { useEffect, useState } from "react";

interface JsonPreviewProps {
  content: string;
}

export default function JsonPreview({ content }: JsonPreviewProps) {
  const [formattedContent, setFormattedContent] = useState("");

  useEffect(() => {
    try {
      if (content) {
        // 尝试解析并重新格式化JSON
        const parsed = JSON.parse(content);
        const formatted = JSON.stringify(parsed, null, 2);
        setFormattedContent(formatted);
      } else {
        setFormattedContent("");
      }
    } catch (error) {
      // 如果不是有效的JSON，直接显示原内容
      setFormattedContent(content);
    }
  }, [content]);

  if (!formattedContent) {
    return (
      <div className="flex items-center justify-center h-full text-muted-foreground bg-muted/30 rounded-lg border border-border">
        <p>JSON preview will appear here</p>
      </div>
    );
  }

  return (
    <div className="relative h-full">
      <pre className="bg-muted/50 rounded-lg p-4 text-sm overflow-auto h-full border border-border text-foreground">
        <code className="language-json text-foreground">{formattedContent}</code>
      </pre>
    </div>
  );
}

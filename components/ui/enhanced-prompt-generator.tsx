"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Co<PERSON>, <PERSON>ader2, <PERSON><PERSON><PERSON>, Trash2, Heart, Settings, Star } from "lucide-react";
import { toast } from "sonner";
import { useAppContext } from "@/contexts/app";
import FavoriteModal from "@/components/ui/favorite-modal";
import { PromptCategory } from "@/types/prompt-category";

export interface EnhancedPromptGeneratorConfig {
  texts: {
    title: string;
    subtitle: string;
    inputLabel: string;
    inputPlaceholder: string;
    outputLabel: string;
    outputPlaceholder: string;
    generateButton: string;
    generating: string;
    generatingText: string;
    waitingText: string;
    copyButton: string;
    favoriteButton: string;
    modeBasic: string;
    modeAdvanced: string;
    errorNoInput: string;
    errorGenerateFailed: string;
    errorNoContent: string;
    errorCopyFailed: string;
    errorGetCategoriesFailed: string;
    errorGetCategoriesFailedWithMsg: string;
    errorFavoriteFailed: string;
    successGenerated: string;
    successCopied: string;
    successFavoriteSaved: string;
  };
  features?: {
    enableModeSwitch?: boolean;
    enableFavorite?: boolean;
    enableCopy?: boolean;
  };
  request?: {
    modelType?: string;
    modelName?: string;
  };
}

interface EnhancedPromptGeneratorProps {
  config: EnhancedPromptGeneratorConfig;
  className?: string;
}

export default function EnhancedPromptGenerator({ 
  config, 
  className = "" 
}: EnhancedPromptGeneratorProps) {
  const { user, setShowSignModal } = useAppContext();
  const { texts, features = {}, request = {} } = config;
  
  // 默认功能开关
  const {
    enableModeSwitch = true,
    enableFavorite = true,
    enableCopy = true,
  } = features;

  const [input, setInput] = useState("");
  const [output, setOutput] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [showFavoriteModal, setShowFavoriteModal] = useState(false);
  const [categories, setCategories] = useState<PromptCategory[]>([]);
  const [isLoadingCategories, setIsLoadingCategories] = useState(false);
  const [mode, setMode] = useState<"basic" | "advanced">("basic");

  // 监听用户登录状态，恢复临时保存的内容
  useEffect(() => {
    if (user) {
      const tempOutput = localStorage.getItem('temp_enhanced_prompt_output');
      if (tempOutput) {
        setOutput(tempOutput);
        localStorage.removeItem('temp_enhanced_prompt_output');
        // 自动打开收藏模态框
        if (enableFavorite) {
          setShowFavoriteModal(true);
        }
      }
      // 登录后默认切换到高级模式
      if (enableModeSwitch) {
        setMode("advanced");
      }
    } else {
      // 未登录时默认基础模式
      setMode("basic");
    }
  }, [user, enableFavorite, enableModeSwitch]);

  const requestEnhancePrompt = async (input: string, mode: string, locale: string) => {
    const requestBody: any = {
      input,
      mode,
      locale,
    };
    requestBody.model_type = request.modelType;
    requestBody.model_name = request.modelName;

    const response = await fetch("/api/generate/enhance-prompt", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(requestBody),
    });
    return response.json();
  };

  const handleGenerate = async () => {
    if (!input.trim()) {
      toast.error(texts.errorNoInput);
      return;
    }

    setIsGenerating(true);
    
    try {
      const locale = document.documentElement.lang || "en";
      const response = await requestEnhancePrompt(input, mode, locale);
      
      if (response.code === -1) {
        toast.error(response.message);
        return;
      }

      setOutput(response.data.text);
      toast.success(texts.successGenerated);
    } catch (error) {
      console.error(error);
      toast.error(texts.errorGenerateFailed);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleCopyOutput = async () => {
    if (!output) {
      toast.error(texts.errorNoContent);
      return;
    }

    try {
      await navigator.clipboard.writeText(output);
      toast.success(texts.successCopied);
    } catch (error) {
      toast.error(texts.errorCopyFailed);
    }
  };

  const handleClearOutput = () => {
    setOutput("");
  };

  const handleClearInput = () => {
    setInput("");
  };

  const handleModeChange = (newMode: "basic" | "advanced") => {
    // 如果用户未登录且尝试切换到高级模式，弹出登录模态框
    if (!user && newMode === "advanced") {
      setShowSignModal(true);
      return;
    }
    
    setMode(newMode);
  };

  const handleFavorite = async () => {
    // 检查用户登录状态
    if (!user) {
      // 保存当前输出内容到 localStorage
      localStorage.setItem('temp_enhanced_prompt_output', output);
      setShowSignModal(true);
      return;
    }
    
    // 用户已登录，获取分类并打开收藏模态框
    await fetchCategories();
    setShowFavoriteModal(true);
  };

  const fetchCategories = async () => {
    setIsLoadingCategories(true);
    try {
      const response = await fetch('/api/prompt/categories');
      const data = await response.json();
      
      if (data.code === 0) {
        const categoriesData = data.data.categories || [];
        setCategories(categoriesData);
      } else {
        toast.error(texts.errorGetCategoriesFailedWithMsg.replace('{message}', data.message || '未知错误'));
      }
    } catch (error) {
      console.error('获取分类异常:', error);
      toast.error(texts.errorGetCategoriesFailed);
    } finally {
      setIsLoadingCategories(false);
    }
  };

  const createNewCategory = async (name: string) => {
    try {
      const response = await fetch('/api/prompt/categories', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name: name.trim() }),
      });

      const data = await response.json();
      if (data.code === 0) {
        return data.data.uuid;
      } else {
        // 根据错误类型显示不同的提示
        if (data.message.includes("already exists")) {
          throw new Error(`category "${name.trim()}" already exists, please use another name`);
        } else {
          throw new Error(data.message || texts.errorFavoriteFailed);
        }
      }
    } catch (error) {
      throw error;
    }
  };

  const handleFavoriteSave = async (data: {
    title: string;
    content: string;
    notes: string;
    categoryUuid: string | null;
    isNewCategory: boolean;
    newCategoryName: string;
  }) => {
    try {
      let categoryUuid = data.categoryUuid;

      const response = await fetch('/api/prompt/favorites', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: data.title,
          prompt: data.content,
          category_uuid: categoryUuid || "",
          notes: data.notes || "",
        }),
      });

      const result = await response.json();
      if (result.code === 0) {
        toast.success(texts.successFavoriteSaved);
      } else {
        toast.error(result.message || texts.errorFavoriteFailed);
        throw new Error(result.message || texts.errorFavoriteFailed);
      }
    } catch (error) {
      toast.error(error instanceof Error ? error.message : texts.errorFavoriteFailed);
      throw error;
    }
  };

  return (
    <section className={`enhanced-prompt-generator ${className}`} id="generator">
      <div className="container">
        <Card className="w-full max-w-7xl mx-auto">
        <CardHeader className="text-center">
          <CardTitle className="flex items-center justify-center gap-2 text-2xl">
            <Sparkles className="h-6 w-6 text-primary" />
            {texts.title}
          </CardTitle>
          <p className="text-muted-foreground">
            {texts.subtitle}
          </p>
        </CardHeader>
        
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 左侧输入区域 */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="input" className="text-base font-medium">
                  {texts.inputLabel}
                </Label>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleClearInput}
                  className="h-8 w-8 p-0 hover:bg-destructive/10 hover:text-destructive"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
              
              <Separator />
              
              <Textarea
                id="input"
                placeholder={texts.inputPlaceholder}
                value={input}
                onChange={(e) => setInput(e.target.value)}
                className="min-h-[250px] resize-none"
              />

              <div className="flex flex-col sm:flex-row gap-3">
                {enableModeSwitch && (
                  <Button
                    variant="outline"
                    size="default"
                    onClick={() => handleModeChange(mode === "basic" ? "advanced" : "basic")}
                    className={`
                      flex items-center gap-2 transition-all duration-300 w-full sm:w-auto h-10 sm:h-11
                      ${mode === "advanced" 
                        ? "bg-gradient-to-r from-purple-500 to-purple-600 text-white border-purple-500 hover:from-purple-600 hover:to-purple-700" 
                        : "bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200"
                      }
                      ${!user && mode === "basic" ? "hover:bg-yellow-50 hover:border-yellow-300" : ""}
                    `}
                  >
                    {mode === "basic" ? (
                      <>
                        <Settings className="h-4 w-4" />
                        {texts.modeBasic}
                      </>
                    ) : (
                      <>
                        <Star className="h-4 w-4" />
                        {texts.modeAdvanced}
                      </>
                    )}
                  </Button>
                )}
                
                <Button 
                  onClick={handleGenerate}
                  disabled={isGenerating || !input.trim()}
                  size="default"
                  className="flex-1 w-full sm:w-auto h-10 sm:h-11"
                >
                  {isGenerating ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {texts.generating}
                    </>
                  ) : (
                    <>
                      <Sparkles className="mr-2 h-4 w-4" />
                      {texts.generateButton}
                    </>
                  )}
                </Button>
              </div>
            </div>

            {/* 右侧输出区域 */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label className="text-base font-medium">
                  {texts.outputLabel}
                </Label>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleClearOutput}
                  className="h-8 w-8 p-0 hover:bg-destructive/10 hover:text-destructive"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
              
              <Separator />
              
              <div className="relative">
                <Textarea
                  placeholder={texts.outputPlaceholder}
                  value={output}
                  onChange={(e) => setOutput(e.target.value)}
                  className="min-h-[250px] resize-none"
                />
                
                {!output && !isGenerating && (
                  <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                    <div className="text-center text-muted-foreground">
                      <Sparkles className="h-12 w-12 mx-auto mb-3 opacity-20" />
                      {/* <p>{texts.waitingText}</p> */}
                    </div>
                  </div>
                )}
                
                {isGenerating && (
                  <div className="absolute inset-0 flex items-center justify-center pointer-events-none bg-background/80">
                    <div className="text-center">
                      <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2 text-primary" />
                      <p className="text-sm text-muted-foreground">{texts.generatingText}</p>
                    </div>
                  </div>
                )}
              </div>

              {output && (
                <div className="flex flex-col sm:flex-row gap-2">
                  {enableCopy && (
                    <Button
                      variant="outline"
                      size="default"
                      onClick={handleCopyOutput}
                      className="flex-1 w-full sm:w-auto h-10 sm:h-11"
                    >
                      <Copy className="h-4 w-4 mr-2" />
                      {texts.copyButton}
                    </Button>
                  )}
                  {enableFavorite && (
                    <Button
                      variant="outline"
                      size="default"
                      onClick={handleFavorite}
                      className="flex-1 w-full sm:w-auto h-10 sm:h-11"
                    >
                      <Heart className="h-4 w-4 mr-2" />
                      {texts.favoriteButton}
                    </Button>
                  )}
                </div>
              )}
            </div>
          </div>
        </CardContent>

        {/* 收藏模态框 */}
        {enableFavorite && (
          <FavoriteModal
            open={showFavoriteModal}
            onOpenChange={setShowFavoriteModal}
            mode="create"
            initialData={{
              content: output,
            }}
            categories={categories}
            isLoadingCategories={isLoadingCategories}
            onSave={handleFavoriteSave}
            onCreateCategory={createNewCategory}
          />
        )}
      </Card>
      </div>
    </section>
  );
} 
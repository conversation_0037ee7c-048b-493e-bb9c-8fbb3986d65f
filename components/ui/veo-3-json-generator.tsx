"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Veo3JsonPromptAIGeneratorConfig, Veo3JsonPromptTemplateGeneratorConfig, Veo3Mode } from "@/types/veo-3";
import TemplateMode from "./veo-3-template-mode";
import AiMode from "./veo-3-ai-mode";

interface Veo3JsonGeneratorProps {
  config: Veo3JsonPromptTemplateGeneratorConfig;
  aiModeConfig: Veo3JsonPromptAIGeneratorConfig;
}

export default function Veo3JsonGenerator({ config, aiModeConfig }: Veo3JsonGeneratorProps) {
  const [mode, setMode] = useState<Veo3Mode>("template");

  // 初始化时从 localStorage 恢复状态
  useEffect(() => {
    const savedMode = localStorage.getItem('veo3_current_mode') as Veo3Mode;
    if (savedMode && (savedMode === "template" || savedMode === "ai")) {
      setMode(savedMode);
    }
  }, []);

  // 清理过期的 localStorage 数据
  useEffect(() => {
    const cleanupExpiredData = () => {
      const keys = ['veo3_current_mode', 'veo3_ai_input', 'veo3_ai_mode'];
      const expireTime = 24 * 60 * 60 * 1000; // 24小时
      
      keys.forEach(key => {
        const timestamp = localStorage.getItem(`${key}_timestamp`);
        if (timestamp && Date.now() - parseInt(timestamp) > expireTime) {
          localStorage.removeItem(key);
          localStorage.removeItem(`${key}_timestamp`);
        }
      });
    };

    cleanupExpiredData();
  }, []);

  // 保存模式变化到 localStorage
  const handleModeChange = (value: string) => {
    const newMode = value as Veo3Mode;
    setMode(newMode);
    localStorage.setItem('veo3_current_mode', newMode);
    localStorage.setItem('veo3_current_mode_timestamp', Date.now().toString());
  };

  return (
    <div className="w-full bg-background">
      <Tabs value={mode} onValueChange={handleModeChange} className="w-full">
        <div className="flex justify-center mb-8">
          <TabsList className="grid w-full max-w-md grid-cols-2 bg-card border border-border">
            <TabsTrigger value="template" className="flex items-center gap-2 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
              {config.templateMode}
            </TabsTrigger>
            <TabsTrigger value="ai" className="flex items-center gap-2 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
              {config.aiMode}
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="template" className="mt-0">
          <TemplateMode config={config} />
        </TabsContent>

        <TabsContent value="ai" className="mt-0">
          <AiMode config={aiModeConfig} />
        </TabsContent>
      </Tabs>
    </div>
  );
}

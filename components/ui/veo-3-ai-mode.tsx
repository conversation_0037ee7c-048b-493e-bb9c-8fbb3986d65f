"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON><PERSON>, <PERSON><PERSON>2, <PERSON><PERSON><PERSON>, Trash2, <PERSON>ap, ExternalLink } from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";
import { Veo3JsonPromptAIGeneratorConfig } from "@/types/veo-3";
import JsonPreview from "./veo-3-json-preview";
import { useAppContext } from "@/contexts/app";
import { useSession } from "next-auth/react";

interface AiModeProps {
  config: Veo3JsonPromptAIGeneratorConfig;
}

interface UserCredits {
  left_credits: number;
}

export default function AiMode({ config }: AiModeProps) {
  const [input, setInput] = useState("");
  const [output, setOutput] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [mode, setMode] = useState<"basic" | "advanced">("basic");
  const [userCredits, setUserCredits] = useState<UserCredits | null>(null);
  const { data: session, status } = useSession();
  const { setShowSignModal } = useAppContext();

  // 初始化时从 localStorage 恢复状态
  useEffect(() => {
    const savedInput = localStorage.getItem('veo3_ai_input');
    const savedMode = localStorage.getItem('veo3_ai_mode') as "basic" | "advanced";
    
    if (savedInput) {
      setInput(savedInput);
    }
    
    if (savedMode && (savedMode === "basic" || savedMode === "advanced")) {
      setMode(savedMode);
    }
  }, []);

  // 实时保存输入内容
  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setInput(value);
    localStorage.setItem('veo3_ai_input', value);
  };

  // 获取用户积分
  useEffect(() => {
    if (session?.user) {
      fetchUserCredits();
    }
  }, [session]);

  const fetchUserCredits = async () => {
    try {
      const response = await fetch("/api/get-user-credits", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });
      if (response.ok) {
        const data = await response.json();
        setUserCredits(data.data);
      }
    } catch (error) {
      console.error("Failed to fetch user credits:", error);
    }
  };

  // 监听登录状态变化，恢复高级模式
  useEffect(() => {
    if (status === "authenticated") {
      const savedMode = localStorage.getItem('veo3_ai_mode') as "basic" | "advanced";
      if (savedMode === "advanced") {
        setMode("advanced");
      }
    }
  }, [status]);

  const handleGenerate = async () => {
    if (!input.trim()) {
      toast.error(config.errorNoInput);
      return;
    }

    // 清除之前的输出结果
    setOutput("");

    // 高级模式需要检查登录状态和积分
    if (mode === "advanced") {
      if (status === "unauthenticated") {
        toast.error(config.errorNotLoggedIn);
        setShowSignModal(true);
        return;
      }

      if (!userCredits || userCredits.left_credits < 1) {
        toast.error(config.errorInsufficientCredits);
        return;
      }
    }

    setIsGenerating(true);
    
    try {
      const locale = document.documentElement.lang || "en";
      const response = await fetch("/api/generate/enhance-prompt", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          input: input.trim(),
          mode: mode,
          locale,
          model_type: "video",
          model_name: "veo-3-json",
        }),
      });

      const result = await response.json();
      
      if (result.code === -1) {
        toast.error(result.message);
        return;
      }

      setOutput(result.data.text);
      toast.success(config.successGenerated);
      
      // 如果是高级模式，更新积分
      if (mode === "advanced") {
        await fetchUserCredits();
      }
    } catch (error) {
      console.error("Error generating JSON:", error);
      toast.error(config.errorGenerateFailed);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleCopyOutput = async () => {
    if (!output) {
      toast.error(config.errorCopyFailed);
      return;
    }

    try {
      await navigator.clipboard.writeText(output);
      toast.success(config.successCopied);
    } catch (error) {
      toast.error(config.errorCopyFailed);
    }
  };

  const handleClear = () => {
    setInput("");
    setOutput("");
    // 清理 localStorage 中的输入内容
    localStorage.removeItem('veo3_ai_input');
  };

  const handleModeToggle = () => {
    const newMode = mode === "basic" ? "advanced" : "basic";
    
    // 切换到高级模式时检查登录状态，弹出登录模态框而不是跳转
    if (newMode === "advanced" && status === "unauthenticated") {
      // 保存当前状态到 localStorage
      localStorage.setItem('veo3_ai_mode', 'advanced');
      setShowSignModal(true);
      return;
    }
    
    setMode(newMode);
    localStorage.setItem('veo3_ai_mode', newMode);
  };

  // 检查高级模式是否可用
  const isAdvancedModeDisabled = mode === "advanced" && 
    (status === "unauthenticated" || !userCredits || userCredits.left_credits < 1);

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 items-start">
      {/* 左侧：输入区域 */}
      <div className="space-y-6">
        <Card className="h-[500px] flex flex-col">
          <CardHeader className="flex flex-row items-center justify-between flex-shrink-0">
            <CardTitle>{config.inputPrompt}</CardTitle>
            <div className="flex items-center gap-2">
              <Button
                variant={mode === "basic" ? "outline" : "default"}
                size="sm"
                onClick={handleModeToggle}
                disabled={isGenerating}
                className={mode === "advanced" ? 
                  "bg-gradient-to-r from-purple-500 to-purple-600 text-white hover:from-purple-600 hover:to-purple-700" : 
                  ""}
              >
                {mode === "basic" ? (
                  <>{config.basicModeLabel}</>
                ) : (
                  <>
                    <Zap className="h-4 w-4 mr-1" />
                    {config.advancedModeLabel}
                  </>
                )}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleClear}
                disabled={isGenerating}
                className="flex items-center gap-2"
              >
                <Trash2 className="h-4 w-4" />
                Clear
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-4 flex flex-col flex-1">
            {/* 模式提示卡片 */}
            {mode === "basic" ? (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 flex-shrink-0">
                <div className="flex items-center gap-2 text-blue-800">
                  <Sparkles className="h-4 w-4" />
                  <span className="text-sm font-medium">
                    {config.basicModeHint}
                  </span>
                </div>
              </div>
            ) : (
              <div className="bg-purple-50 border border-purple-200 rounded-lg p-3 flex-shrink-0">
                <div className="flex items-center gap-2 text-purple-800">
                  <Zap className="h-4 w-4" />
                  <span className="text-sm font-medium">
                    {config.advancedModeHint}
                  </span>
                </div>
              </div>
            )}

            <Textarea
              id="prompt-input"
              placeholder={config.inputPlaceholder}
              value={input}
              onChange={handleInputChange}
              className="resize-none bg-input border-border text-foreground placeholder:text-muted-foreground flex-1"
            />

            <div className="space-y-2 flex-shrink-0">
              <Button
                onClick={handleGenerate}
                disabled={isGenerating || !input.trim() || isAdvancedModeDisabled}
                className="w-full"
              >
                {isGenerating ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {mode === "basic" ? config.generating : config.advancedGenerating}
                  </>
                ) : (
                  <>
                    <Sparkles className="mr-2 h-4 w-4" />
                    {mode === "basic" ? config.generateButton : config.advancedGenerateButton}
                  </>
                )}
              </Button>

              {/* 积分提示 - 紧贴生成按钮下方 */}
              {mode === "advanced" && userCredits && (
                <div className="text-center text-sm text-muted-foreground">
                  {userCredits.left_credits > 0 ? (
                    <span>
                      {config.creditsRemaining}: {userCredits.left_credits}
                    </span>
                  ) : (
                    <div className="flex items-center justify-center gap-1 text-orange-600">
                      <span>{config.creditsInsufficient}</span>
                      <Link
                        href="/pricing"
                        className="inline-flex items-center gap-1 text-blue-600 hover:text-blue-800 underline"
                      >
                        {config.buyCredits}
                        <ExternalLink className="h-3 w-3" />
                      </Link>
                    </div>
                  )}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 右侧：输出区域 */}
      <div className="space-y-6">
        <Card className="h-[500px] flex flex-col">
          <CardHeader className="flex flex-row items-center justify-between flex-shrink-0">
            <CardTitle>{config.jsonPreview}</CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={handleCopyOutput}
              disabled={!output}
              className="flex items-center gap-2"
            >
              <Copy className="h-4 w-4" />
              {config.copyJson}
            </Button>
          </CardHeader>
          <CardContent className="flex flex-col flex-1 min-h-0">
            {output ? (
              <div className="flex-1 min-h-0">
                <JsonPreview content={output} />
              </div>
            ) : (
              <div className="flex items-center justify-center flex-1 text-muted-foreground bg-muted/30 rounded-lg border border-border">
                <div className="text-center">
                  <Sparkles className="h-12 w-12 mx-auto mb-4 opacity-50 text-muted-foreground" />
                  <p className="text-muted-foreground">{config.emptyStateText}</p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

import { cn } from "@/lib/utils";

interface HtmlContentProps {
  content: string;
  className?: string;
}

export default function HtmlContent({ content, className }: HtmlContentProps) {
  return (
    <div
      className={cn(
        "prose prose-lg max-w-none dark:prose-invert prose-enhanced",
        // Light mode specific styles for better readability
        "prose-headings:text-foreground prose-p:text-foreground prose-li:text-foreground",
        "prose-strong:text-foreground prose-em:text-foreground",
        "prose-blockquote:text-muted-foreground",
        "prose-code:text-foreground prose-pre:text-foreground",
        // General prose styles
        "prose-headings:scroll-m-20 prose-headings:tracking-tight",
        "prose-h1:text-3xl prose-h1:font-bold prose-h1:mb-6 prose-h1:mt-8",
        "prose-h2:text-2xl prose-h2:font-semibold prose-h2:mb-4 prose-h2:mt-8",
        "prose-h3:text-xl prose-h3:font-semibold prose-h3:mb-3 prose-h3:mt-6",
        "prose-h4:text-lg prose-h4:font-semibold prose-h4:mb-2 prose-h4:mt-4",
        "prose-p:leading-relaxed prose-p:mb-4",
        "prose-a:text-primary prose-a:no-underline hover:prose-a:underline prose-a:transition-colors",
        "prose-ul:my-4 prose-ol:my-4",
        "prose-li:my-1 prose-li:marker:text-primary",
        "prose-strong:font-semibold",
        "prose-em:italic",
        "prose-hr:border-border prose-hr:my-8",
        className
      )}
      dangerouslySetInnerHTML={{ __html: content }}
    />
  );
} 
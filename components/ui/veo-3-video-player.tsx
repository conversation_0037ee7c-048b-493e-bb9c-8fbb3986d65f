"use client";

import { useState, useRef } from "react";
import { Play, Pause } from "lucide-react";
import { Button } from "@/components/ui/button";
import Image from "next/image";

interface VideoPlayerProps {
  coverImage: string;
  videoUrl: string;
  title: string;
}

export default function VideoPlayer({ coverImage, videoUrl, title }: VideoPlayerProps) {
  const [isPlaying, setIsPlaying] = useState(false);
  const [showVideo, setShowVideo] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);

  const handlePlayClick = () => {
    if (!showVideo) {
      setShowVideo(true);
      setTimeout(() => {
        if (videoRef.current) {
          videoRef.current.play();
          setIsPlaying(true);
        }
      }, 100);
    } else {
      if (videoRef.current) {
        if (isPlaying) {
          videoRef.current.pause();
          setIsPlaying(false);
        } else {
          videoRef.current.play();
          setIsPlaying(true);
        }
      }
    }
  };

  const handleVideoEnded = () => {
    setIsPlaying(false);
  };

  const handleVideoPause = () => {
    setIsPlaying(false);
  };

  const handleVideoPlay = () => {
    setIsPlaying(true);
  };

  return (
    <div className="relative w-full">
      <div className="relative aspect-video bg-black rounded-lg overflow-hidden">
        {!showVideo ? (
          // 显示封面图
          <div className="relative w-full h-full">
            <Image
              src={coverImage}
              alt={title}
              fill
              className="object-cover"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />
            <div className="absolute inset-0 bg-black/20 flex items-center justify-center">
              <Button
                onClick={handlePlayClick}
                size="lg"
                className="rounded-full w-16 h-16 p-0 bg-white/90 hover:bg-white text-black"
              >
                <Play className="h-8 w-8 ml-1" />
              </Button>
            </div>
          </div>
        ) : (
          // 显示视频
          <div className="relative w-full h-full">
            <video
              ref={videoRef}
              src={videoUrl}
              className="w-full h-full object-cover"
              onEnded={handleVideoEnded}
              onPause={handleVideoPause}
              onPlay={handleVideoPlay}
              controls
            />
          </div>
        )}
      </div>
      
      <div className="mt-2">
        <p className="text-sm text-muted-foreground text-center">{title}</p>
      </div>
    </div>
  );
}

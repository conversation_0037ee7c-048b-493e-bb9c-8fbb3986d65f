"use client";

import { useState, useEffect } from "react";
import { useTranslations } from "next-intl";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Loader2 } from "lucide-react";
import { toast } from "sonner";
import { PromptCategory } from "@/types/prompt-category";

export interface FavoriteModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  mode: "create" | "edit";
  initialData?: {
    uuid?: string;
    title?: string;
    content?: string;
    notes?: string;
    categoryUuid?: string;
  };
  categories: PromptCategory[];
  isLoadingCategories?: boolean;
  onSave: (data: {
    title: string;
    content: string;
    notes: string;
    categoryUuid: string | null;
    isNewCategory: boolean;
    newCategoryName: string;
  }) => Promise<void>;
  onCreateCategory?: (name: string) => Promise<string>;
}

export default function FavoriteModal({
  open,
  onOpenChange,
  mode,
  initialData,
  categories,
  isLoadingCategories = false,
  onSave,
  onCreateCategory,
}: FavoriteModalProps) {
  const t = useTranslations("prompt_generator");
  
  // 表单状态
  const [title, setTitle] = useState("");
  const [content, setContent] = useState("");
  const [notes, setNotes] = useState("");
  const [selectedCategoryId, setSelectedCategoryId] = useState("");
  const [isCreatingNewCategory, setIsCreatingNewCategory] = useState(false);
  const [newCategoryName, setNewCategoryName] = useState("");
  const [isSaving, setIsSaving] = useState(false);

  // 初始化表单数据
  useEffect(() => {
    if (open && initialData) {
      setTitle(initialData.title || "");
      setContent(initialData.content || "");
      setNotes(initialData.notes || "");
      setSelectedCategoryId(initialData.categoryUuid || "");
    } else if (open && !initialData) {
      // 重置表单
      setTitle("");
      setContent("");
      setNotes("");
      setSelectedCategoryId("");
    }
    
    // 重置其他状态
    setIsCreatingNewCategory(false);
    setNewCategoryName("");
    setIsSaving(false);
  }, [open, initialData]);

  // 处理分类选择变化
  const handleCategoryChange = (value: string) => {
    if (value === "__create_new__") {
      setIsCreatingNewCategory(true);
      setSelectedCategoryId("");
    } else {
      setIsCreatingNewCategory(false);
      setSelectedCategoryId(value);
      setNewCategoryName("");
    }
  };

  // 处理保存
  const handleSave = async () => {
    // 验证必填字段
    if (!title.trim()) {
      toast.error(t("error_title_required"));
      return;
    }

    if (!content.trim()) {
      toast.error(t("error_content_required"));
      return;
    }

    if (isCreatingNewCategory && !newCategoryName.trim()) {
      toast.error(t("error_category_name_required"));
      return;
    }

    setIsSaving(true);
    try {
      let categoryUuid = selectedCategoryId || null;

      // 如果选择新建分类，先创建分类
      if (isCreatingNewCategory && newCategoryName.trim() && onCreateCategory) {
        categoryUuid = await onCreateCategory(newCategoryName.trim());
      }

      await onSave({
        title: title.trim(),
        content: content.trim(),
        notes: notes.trim(),
        categoryUuid,
        isNewCategory: isCreatingNewCategory,
        newCategoryName: newCategoryName.trim(),
      });

      // 成功后关闭模态框
      onOpenChange(false);
    } catch (error) {
      // 错误处理在父组件中进行
      console.error("Save favorite error:", error);
    } finally {
      setIsSaving(false);
    }
  };

  // 获取模态框标题
  const getModalTitle = () => {
    switch (mode) {
      case "create":
        return t("favorite_modal_title");
      case "edit":
        return t("favorite_modal_edit_title");
      default:
        return t("favorite_modal_title");
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{getModalTitle()}</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* 标题字段 */}
          <div className="space-y-2">
            <Label htmlFor="title">{t("favorite_title_required")}</Label>
            <Input
              id="title"
              placeholder={t("favorite_title_placeholder")}
              value={title}
              onChange={(e) => setTitle(e.target.value)}
            />
          </div>

          {/* 分类字段 */}
          <div className="space-y-2">
            <Label htmlFor="category">{t("favorite_category_label")}</Label>
            <select
              id="category"
              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              value={isCreatingNewCategory ? "__create_new__" : selectedCategoryId}
              onChange={(e) => handleCategoryChange(e.target.value)}
              disabled={isLoadingCategories}
            >
              <option value="">{t("favorite_category_placeholder")}</option>
              {categories.map((category) => (
                <option key={category.uuid} value={category.uuid}>
                  {category.name}
                </option>
              ))}
              <option value="__create_new__">{t("favorite_category_new")}</option>
            </select>
            
            {isCreatingNewCategory && (
              <Input
                placeholder={t("favorite_category_new_placeholder")}
                value={newCategoryName}
                onChange={(e) => setNewCategoryName(e.target.value)}
                className="mt-2"
              />
            )}
          </div>

          {/* 内容字段 */}
          <div className="space-y-2">
            <Label htmlFor="content">{t("favorite_content_label")}</Label>
            <Textarea
              id="content"
              value={content}
              onChange={(e) => setContent(e.target.value)}
              className="min-h-[100px]"
              placeholder={t("favorite_content_placeholder")}
            />
          </div>

          {/* 备注字段 */}
          <div className="space-y-2">
            <Label htmlFor="notes">{t("favorite_notes_label")}</Label>
            <Textarea
              id="notes"
              placeholder={t("favorite_notes_placeholder")}
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              className="min-h-[80px]"
            />
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isSaving}
          >
            {t("favorite_cancel")}
          </Button>
          <Button
            onClick={handleSave}
            disabled={isSaving || !title.trim() || (isCreatingNewCategory && !newCategoryName.trim())}
          >
            {isSaving ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {t("favorite_saving")}
              </>
            ) : (
              t("favorite_save")
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 
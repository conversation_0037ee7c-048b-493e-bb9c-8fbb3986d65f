import Icon from "@/components/icon";
import { TipsSection } from "@/types/blocks/tips";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export default function Tips({ section }: { section: TipsSection }) {
  if (section.disabled) {
    return null;
  }

  return (
    <section id={section.name} className="py-16">
      <div className="container">
        <div className="mx-auto flex max-w-4xl flex-col items-center gap-2 text-center">
          <h2 className="mb-2 text-pretty text-3xl font-bold lg:text-4xl">
            {section.title}
          </h2>
          {section.subtitle && (
            <p className="mb-4 text-xl font-medium text-primary">
              {section.subtitle}
            </p>
          )}
          {section.description && (
            <p className="mb-8 max-w-2xl text-muted-foreground lg:text-lg">
              {section.description}
            </p>
          )}
        </div>
        
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {section.tips?.map((tip, index) => (
            <Card key={tip.id || index} className="group h-full transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
              <CardHeader className="pb-4">
                <div className="flex items-center gap-3">
                  {tip.icon && (
                    <div className="flex size-12 items-center justify-center rounded-lg bg-primary/10 text-primary group-hover:bg-primary group-hover:text-primary-foreground transition-colors duration-300">
                      <Icon name={tip.icon} className="size-6" />
                    </div>
                  )}
                  <CardTitle className="text-lg leading-tight">
                    <h3>{tip.title}</h3>
                  </CardTitle>
                </div>
                {tip.category && (
                  <div className="mt-2">
                    <span className="inline-flex items-center rounded-full bg-primary/10 px-2.5 py-0.5 text-xs font-medium text-primary">
                      {tip.category}
                    </span>
                  </div>
                )}
              </CardHeader>
              
              <CardContent className="pt-0">
                <CardDescription className="text-base leading-relaxed mb-4">
                  {tip.description}
                </CardDescription>
                
                {tip.example && (
                  <div className="rounded-md bg-muted p-3 text-sm">
                    <div className="mb-2 font-medium text-foreground">{section.example_label || "示例"}：</div>
                    <code className="text-muted-foreground">{tip.example}</code>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
} 
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import Icon from "@/components/icon";
import { AboutPage } from "@/types/pages/about";

export default function About({ data }: { data: AboutPage }) {
  return (
    <div className="min-h-screen bg-background">
      {/* 关于Prompt Ark - Hero风格区域 */}
      <section className="py-20 lg:py-32">
        <div className="container">
          <div className="text-center">
            <h1 className="mb-6 text-4xl font-bold tracking-tight lg:text-6xl">
              {data.hero?.title}
            </h1>
            <p className="mx-auto max-w-3xl text-lg text-muted-foreground lg:text-xl">
              {data.hero?.description}
            </p>
          </div>
        </div>
      </section>

      {/* 使命和愿景 - 左右分栏布局 */}
      <section className="py-20">
        <div className="container">
          <div className="grid gap-8 lg:grid-cols-2 lg:gap-16">
            {/* 使命卡片 */}
            {data.mission && (
              <Card className="p-8 text-center">
                <CardContent className="p-0">
                  <div className="mb-6 flex justify-center">
                    <div className="flex size-16 items-center justify-center rounded-full bg-primary/10">
                      <Icon name={data.mission.icon} className="size-8 text-primary" />
                    </div>
                  </div>
                  <h2 className="mb-4 text-2xl font-bold">{data.mission.title}</h2>
                  <p className="text-muted-foreground">{data.mission.description}</p>
                </CardContent>
              </Card>
            )}

            {/* 愿景卡片 */}
            {data.vision && (
              <Card className="p-8 text-center">
                <CardContent className="p-0">
                  <div className="mb-6 flex justify-center">
                    <div className="flex size-16 items-center justify-center rounded-full bg-primary/10">
                      <Icon name={data.vision.icon} className="size-8 text-primary" />
                    </div>
                  </div>
                  <h2 className="mb-4 text-2xl font-bold">{data.vision.title}</h2>
                  <p className="text-muted-foreground">{data.vision.description}</p>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </section>

      {/* 关于我们的团队 - 卡片布局 */}
      <section className="py-20">
        <div className="container">
          <div className="text-center mb-16">
            <h2 className="mb-4 text-3xl font-bold lg:text-4xl">
              {data.team?.title}
            </h2>
            <p className="mx-auto max-w-2xl text-lg text-muted-foreground">
              {data.team?.description}
            </p>
          </div>

          {/* 团队特色卡片 */}
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            {data.team?.features?.map((feature, index) => (
              <Card key={index} className="p-8 text-center">
                <CardContent className="p-0">
                  <div className="mb-6 flex justify-center">
                    <div className="flex size-16 items-center justify-center rounded-full bg-primary/10">
                      <Icon name={feature.icon} className="size-8 text-primary" />
                    </div>
                  </div>
                  <h3 className="mb-4 text-xl font-bold">{feature.title}</h3>
                  <p className="text-muted-foreground">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
} 
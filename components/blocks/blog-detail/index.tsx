"use client";

import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { 
  ArrowUp,
  User
} from "lucide-react";
import { toast } from "sonner";

import Crumb from "./crumb";
import { Post } from "@/types/post";
import moment from "moment";
import { useEffect, useState } from "react";
import HtmlContent from "@/components/ui/html-content";

export default function BlogDetail({ post }: { post: Post }) {
  const [showBackToTop, setShowBackToTop] = useState(false);
  const [readingProgress, setReadingProgress] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      const docHeight = document.documentElement.scrollHeight - window.innerHeight;
      const progress = (scrollTop / docHeight) * 100;
      
      setReadingProgress(progress);
      setShowBackToTop(scrollTop > 400);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <>
      {/* 阅读进度条 */}
      <div 
        className="reading-progress"
        style={{ transform: `scaleX(${readingProgress / 100})` }}
      />
      
      <section className="relative py-8 md:py-16">
        {/* 背景装饰 */}
        <div className="absolute inset-0 blog-gradient-bg" />
        
        <div className="container relative">
          <Crumb post={post} />
          
          {/* 文章头部 */}
          <div className="max-w-4xl mx-auto">
            {/* 标题区域 */}
            <div className="text-center mb-8 md:mb-12">
              <h1 className="mb-6 text-3xl md:text-4xl lg:text-5xl font-bold tracking-tight blog-title-gradient leading-tight">
                {post.title}
              </h1>
              
              {post.description && (
                <p className="text-lg md:text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
                  {post.description}
                </p>
              )}
            </div>

            {/* 作者信息 */}
            <div className="mb-8 md:mb-12 text-center">
              <div className="flex items-center justify-center gap-3 text-sm">
                <Avatar className="h-8 w-8">
                  {post.author_avatar_url ? (
                    <AvatarImage
                      src={post.author_avatar_url}
                      alt={post.author_name || "Author"}
                    />
                  ) : (
                    <AvatarFallback>
                      <User className="h-4 w-4" />
                    </AvatarFallback>
                  )}
                </Avatar>
                
                <div>
                  <span className="font-medium">
                    {post.author_name || "匿名作者"}
                  </span>
                  <span className="mx-2 text-muted-foreground">·</span>
                  <span className="text-muted-foreground">
                    {post.created_at && moment(post.created_at).format("YYYY-MM-DD")}
                  </span>
                </div>
              </div>
            </div>

            {/* 封面图片 */}
            {post.cover_url && (
              <div className="mb-8 md:mb-12">
                <img
                  src={post.cover_url}
                  alt={`${post.title} 封面图片`}
                  className="w-full h-64 md:h-96 object-cover rounded-xl shadow-2xl"
                  loading="lazy"
                />
              </div>
            )}
          </div>

          {/* 主要内容区域 */}
          <div className="max-w-5xl mx-auto">
            {/* 文章内容 */}
            <main role="main" aria-label="文章内容">
              <Card className="shadow-xl blog-content-card">
                <CardContent className="p-6 md:p-8">
                  {post.content && (
                    <HtmlContent content={post.content} />
                  )}
                </CardContent>
              </Card>
            </main>
          </div>
        </div>
      </section>

      {/* 回到顶部按钮 */}
      {showBackToTop && (
        <Button
          onClick={scrollToTop}
          className="fixed bottom-8 right-8 h-12 w-12 rounded-full shadow-lg z-50 blog-back-to-top"
          size="icon"
          aria-label="回到页面顶部"
        >
          <ArrowUp className="h-5 w-5" aria-hidden="true" />
        </Button>
      )}
    </>
  );
}

"use client";

import { Tweet } from 'react-tweet';
import { cn } from "@/lib/utils";

interface TwitterExample {
  id: string;
  tweetUrl: string;
}

interface TwitterExamplesSection {
  title: string;
  items: TwitterExample[];
}

interface ReactTweetExamplesProps {
  section: TwitterExamplesSection;
  className?: string;
}

export default function ReactTweetExamples({ 
  section, 
  className 
}: ReactTweetExamplesProps) {
  if (!section.items || section.items.length === 0) {
    return null;
  }

  // 从Twitter URL中提取tweet ID
  const extractTweetId = (url: string) => {
    const match = url.match(/status\/(\d+)/);
    return match ? match[1] : null;
  };

  return (
    <section className={cn("py-16", className)} id="examples">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold">{section.title}</h2>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {section.items.map((example) => {
            const tweetId = extractTweetId(example.tweetUrl);
            return tweetId ? (
              <div key={example.id} className="flex justify-center">
                <Tweet id={tweetId} />
              </div>
            ) : null;
          })}
        </div>
      </div>
    </section>
  );
}
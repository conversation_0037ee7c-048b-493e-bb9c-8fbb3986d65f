"use client";

import { useState, useRef, useEffect, useCallback } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { 
  Copy, 
  Loader2, 
  Video, 
  Trash2, 
  Download, 
  Share2, 
  Upload,
  Play,
  Pause,
  Volume2,
  VolumeX,
  Maximize,
  RefreshCw
} from "lucide-react";
import { toast } from "sonner";
import { useAppContext } from "@/contexts/app";
import Image from "next/image";

interface VideoGeneratorProps {
  className?: string;
  texts: {
    title: string;
    subtitle: string;
    promptLabel: string;
    promptPlaceholder: string;
    modelLabel: string;
    imageLabel: string;
    imagePlaceholder: string;
    generateButton: string;
    generating: string;
    generatingText: string;
    copyButton: string;
    downloadButton: string;
    shareButton: string;
    errorNoPrompt: string;
    errorGenerateFailed: string;
    successGenerated: string;
    successCopied: string;
    selectImage: string;
    resultLabel: string;
    resultPlaceholder: string;
    generationFailed: string;
    modelStandard: string;
    modelFast: string;
    errorCreditsNotEnough: string;
    errorUnauthorized: string;
    taskSubmitted: string;
    taskProcessing: string;
    statusQueryFailed: string;
    copyFailed: string;
    shareTitle: string;
    generationTimeout: string;
    uploadingImage: string;
    submittingTask: string;
    taskId: string;
    retryButton: string;
    errorImageTooLarge: string;
    errorInvalidImage: string;
    errorTaskIdFailed: string;
    referenceImageAlt: string;
    downloadPreparing: string;
    downloadFailed: string;
    downloadSuccess: string;
    downloadError: string;
  };
}

interface VideoResult {
  url: string;
  thumbnail?: string;
  duration?: number;
  status: "generating" | "completed" | "failed";
  progress?: number;
  taskId?: string;
}

// 任务状态常量（与后端保持一致）
const TaskStatus = {
  PROCESSING: "processing",
  COMPLETED: "completed", 
  FAILED: "failed"
} as const;



export default function VideoGenerator({ className = "", texts }: VideoGeneratorProps) {
  const { user, setShowSignModal } = useAppContext();
  
  // 表单状态
  const [prompt, setPrompt] = useState("");
  const [model, setModel] = useState("veo3");
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string>("");
  
  // 生成状态
  const [isGenerating, setIsGenerating] = useState(false);
  const [videoResult, setVideoResult] = useState<VideoResult | null>(null);
  const [currentTaskId, setCurrentTaskId] = useState<string | null>(null);
  
  // 视频播放状态
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  
  const videoRef = useRef<HTMLVideoElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const pollIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // 清理轮询
  const clearPolling = useCallback(() => {
    if (pollIntervalRef.current) {
      console.log('清理轮询定时器');
      clearInterval(pollIntervalRef.current);
      pollIntervalRef.current = null;
    }
  }, []);

  // 组件卸载时清理轮询
  useEffect(() => {
    return () => clearPolling();
  }, [clearPolling]);

  // 上传图片到服务器并获取URL
  const uploadImage = async (file: File): Promise<string> => {
    const formData = new FormData();
    formData.append('image', file);
    
    // 这里需要实现图片上传接口，暂时返回本地预览URL
    // 在实际项目中，你需要实现一个图片上传API
    return imagePreview;
  };

  // 提交生成任务
  const submitGenerationTask = async (imageUrl: string, prompt: string, model: string) => {
    console.log('发送任务提交请求:', { imageUrl, prompt, model });
    
    const response = await fetch('/api/generate/veo-3', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        imageUrl,
        prompt,
        model
      })
    });

    console.log('任务提交响应状态:', response.status);
    
    if (!response.ok) {
      console.error('任务提交HTTP错误:', response.status);
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    console.log('任务提交API完整响应:', result);
    
    if (result.code !== 0) {
      console.error('任务提交失败:', result.message);
      throw new Error(result.message || '任务提交失败');
    }

    console.log('任务提交成功，返回数据:', result.data);
    return result.data;
  };

  // 查询任务状态
  const checkTaskStatus = async (taskId: string) => {
    console.log(`查询任务状态, taskId: ${taskId}`);
    const response = await fetch(`/api/generate/veo-3/status?task_id=${taskId}`);
    
    if (!response.ok) {
      console.error(`状态查询HTTP错误: ${response.status}`);
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    console.log('状态查询API响应:', result);
    
    if (result.code !== 0) {
      console.error('状态查询失败:', result.message);
      throw new Error(result.message || '状态查询失败');
    }

    return result.data;
  };

  // 开始轮询任务状态
  const startPolling = useCallback((taskId: string) => {
    console.log('开始轮询任务状态, taskId:', taskId);
    clearPolling(); // 清除之前的轮询
    
    let pollCount = 0;
    const maxPollCount = 200; // 最多轮询200次 (约10分钟)
    let currentProgress = 10;

    const poll = async () => {
      try {
        pollCount++;
        console.log(`轮询第${pollCount}次，taskId: ${taskId}`);
        
        if (pollCount > maxPollCount) {
          console.log('达到最大轮询次数，停止轮询');
          clearPolling();
          setVideoResult(prev => prev ? { ...prev, status: "failed" } : null);
          setIsGenerating(false);
          toast.error(texts.generationTimeout);
          return;
        }

        const statusData = await checkTaskStatus(taskId);
        console.log('轮询状态结果:', statusData);
        
        // 模拟进度增长
        if (statusData.data.task_status === TaskStatus.PROCESSING) {
          currentProgress = Math.min(currentProgress + Math.random() * 5, 90);
          console.log(`任务处理中，进度: ${currentProgress}%`);
          setVideoResult(prev => prev ? { ...prev, progress: currentProgress } : null);
        } else if (statusData.data.task_status === TaskStatus.COMPLETED) {
          // 任务完成
          console.log('任务完成，停止轮询');
          clearPolling();
          setVideoResult({
            url: statusData.data.resultUrls,
            status: "completed",
            progress: 100,
            taskId
          });
          setIsGenerating(false);
          toast.success(texts.successGenerated);
        } else if (statusData.data.task_status === TaskStatus.FAILED) {
          // 任务失败
          console.log('任务失败，停止轮询');
          clearPolling();
          setVideoResult({
            url: "",
            status: "failed",
            taskId
          });
          setIsGenerating(false);
          toast.error(texts.generationFailed);
        }
      } catch (error) {
        console.error('轮询状态失败:', error);
        // 网络错误不立即停止轮询，继续尝试
      }
    };

    // 立即执行一次
    console.log('立即执行第一次轮询');
    poll();
    
    // 设置轮询间隔
    console.log('设置轮询间隔: 5秒');
    pollIntervalRef.current = setInterval(poll, 5000);
  }, [clearPolling, texts.successGenerated]);

  // 处理图片上传
  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type.startsWith('image/')) {
      // 检查文件大小 (限制为10MB)
      if (file.size > 10 * 1024 * 1024) {
        toast.error(texts.errorImageTooLarge);
        return;
      }
      
      setImageFile(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    } else {
      toast.error(texts.errorInvalidImage);
    }
  };

  // 清除图片
  const clearImage = () => {
    setImageFile(null);
    setImagePreview("");
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  // 主要的生成函数
  const handleGenerate = async () => {
    if (!prompt.trim()) {
      toast.error(texts.errorNoPrompt);
      return;
    }

    // 检查登录状态
    if (!user) {
      setShowSignModal(true);
      return;
    }

    setIsGenerating(true);
    setVideoResult({ url: "", status: "generating", progress: 0 });

    try {
      // 1. 如果有图片，先上传图片
      let imageUrl = "";
      if (imageFile) {
        toast.info(texts.uploadingImage);
        imageUrl = await uploadImage(imageFile);
      }

      // 2. 提交生成任务
      toast.info(texts.submittingTask);
      const taskData = await submitGenerationTask(imageUrl, prompt, model);
      const taskId = taskData.data?.taskId;
      
      console.log('提交任务结果:', taskData);
      console.log('获取到的taskId:', taskId);
      
      if (!taskId) {
        throw new Error(texts.errorTaskIdFailed);
      }

      setCurrentTaskId(taskId);
      setVideoResult({
        url: "",
        status: "generating",
        taskId, 
        progress: 10 
      });

      // 3. 开始轮询状态
      toast.info(texts.taskSubmitted);
      console.log('准备开始轮询, taskId:', taskId);
      startPolling(taskId);
      
    } catch (error: any) {
      console.error('生成失败:', error);
      console.error('错误堆栈:', error.stack);
      setIsGenerating(false);
      setVideoResult({ url: "", status: "failed" });
      
      // 处理特定错误消息
      if (error.message.includes('credits not enough')) {
        toast.error(texts.errorCreditsNotEnough);
      } else if (error.message.includes('unauthorized')) {
        toast.error(texts.errorUnauthorized);
        setShowSignModal(true);
      } else {
        toast.error(error.message || texts.errorGenerateFailed);
      }
    }
  };

  // 重试生成
  const handleRetry = () => {
    setVideoResult(null);
    setCurrentTaskId(null);
    handleGenerate();
  };

  // 手动刷新状态
  const handleRefreshStatus = async () => {
    if (!currentTaskId) return;
    
    try {
      const statusData = await checkTaskStatus(currentTaskId);
      
      if (statusData.task_status === TaskStatus.COMPLETED) {
        setVideoResult({
          url: statusData.resultUrls[0] || statusData.resultUrls,
          status: "completed",
          progress: 100,
          taskId: currentTaskId
        });
        setIsGenerating(false);
        clearPolling();
        toast.success(texts.successGenerated);
      } else if (statusData.task_status === TaskStatus.FAILED) {
        setVideoResult({
          url: "",
          status: "failed",
          taskId: currentTaskId
        });
        setIsGenerating(false);
        clearPolling();
        toast.error(texts.generationFailed);
      } else {
        toast.info(texts.taskProcessing);
      }
    } catch (error) {
      toast.error(texts.statusQueryFailed);
    }
  };

  // 复制视频链接
  const handleCopyLink = async () => {
    if (!videoResult?.url) return;
    
    try {
      await navigator.clipboard.writeText(videoResult.url);
      toast.success(texts.successCopied);
    } catch (error) {
      toast.error(texts.copyFailed);
    }
  };

  // 下载视频
  const handleDownload = async () => {
    if (!videoResult?.url) return;
    
    try {
      // 显示下载提示
      toast.info(texts.downloadPreparing);
      
      // 使用fetch获取视频数据
      const response = await fetch(videoResult.url);
      if (!response.ok) throw new Error(texts.downloadFailed);
      
      // 创建blob
      const blob = await response.blob();
      
      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `veo3-video-${Date.now()}.mp4`;
      document.body.appendChild(link);
      link.click();
      
      // 清理
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      toast.success(texts.downloadSuccess);
    } catch (error) {
      console.error('下载失败:', error);
      toast.error(texts.downloadError);
    }
  };

  // 分享视频
  const handleShare = async () => {
    if (!videoResult?.url) return;
    
    if (navigator.share) {
      try {
        await navigator.share({
          title: texts.shareTitle,
          url: videoResult.url
        });
      } catch (error) {
        handleCopyLink();
      }
    } else {
      handleCopyLink();
    }
  };

  // 视频播放控制
  const togglePlay = () => {
    if (!videoRef.current) return;
    
    if (isPlaying) {
      videoRef.current.pause();
    } else {
      videoRef.current.play();
    }
    setIsPlaying(!isPlaying);
  };

  const toggleMute = () => {
    if (!videoRef.current) return;
    
    videoRef.current.muted = !isMuted;
    setIsMuted(!isMuted);
  };

  // 清除输出
  const clearOutput = () => {
    clearPolling();
    setVideoResult(null);
    setCurrentTaskId(null);
    setIsPlaying(false);
    setCurrentTime(0);
    setDuration(0);
    setIsGenerating(false);
  };

  return (
    <section className={`video-generator ${className}`} id="generator">
      <div className="container">
        <Card className="w-full max-w-7xl mx-auto">
          <CardHeader className="text-center">
            <CardTitle className="flex items-center justify-center gap-2 text-2xl">
              <Video className="h-6 w-6 text-primary" />
              {texts.title}
            </CardTitle>
            <p className="text-muted-foreground">
              {texts.subtitle}
            </p>
          </CardHeader>
          
          <CardContent>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 左侧输入区域 */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="prompt" className="text-base font-medium">
                    {texts.promptLabel}
                  </Label>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setPrompt("")}
                    className="h-8 w-8 p-0 hover:bg-destructive/10 hover:text-destructive"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
                
                <Separator />
                
                <Textarea
                  id="prompt"
                  placeholder={texts.promptPlaceholder}
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                  className="min-h-[180px] resize-none"
                  disabled={isGenerating}
                />

                {/* 模型选择 */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">
                    {texts.modelLabel}
                  </Label>
                  <Select value={model} onValueChange={setModel} disabled={isGenerating}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="veo3">{texts.modelStandard}</SelectItem>
                      <SelectItem value="veo3_fast">{texts.modelFast}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* 参考图片上传 */}
                {/* <div className="space-y-2">
                  <Label className="text-sm font-medium">
                    {texts.imageLabel}
                  </Label>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Input
                        ref={fileInputRef}
                        type="file"
                        accept="image/*"
                        onChange={handleImageUpload}
                        className="hidden"
                        disabled={isGenerating}
                      />
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => fileInputRef.current?.click()}
                        className="flex-1"
                        disabled={isGenerating}
                      >
                        <Upload className="h-4 w-4 mr-2" />
                        {texts.selectImage}
                      </Button>
                      {imageFile && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={clearImage}
                          className="h-8 w-8 p-0 hover:bg-destructive/10 hover:text-destructive"
                          disabled={isGenerating}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                    
                    {imagePreview && (
                      <div className="relative w-full h-32 bg-muted rounded-md overflow-hidden">
                        <Image
                          src={imagePreview}
                          alt={texts.referenceImageAlt}
                          fill
                          className="object-cover"
                        />
                      </div>
                    )}
                  </div>
                </div> */}

                <Button 
                  onClick={handleGenerate}
                  disabled={isGenerating || !prompt.trim()}
                  size="lg"
                  className="w-full"
                >
                  {isGenerating ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {texts.generating}
                    </>
                  ) : (
                    <>
                      <Video className="mr-2 h-4 w-4" />
                      {texts.generateButton}
                    </>
                  )}
                </Button>
              </div>

              {/* 右侧输出区域 */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label className="text-base font-medium">
                    {texts.resultLabel}
                  </Label>
                  <div className="flex items-center gap-2">
                    {isGenerating && currentTaskId && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleRefreshStatus}
                        className="h-8 w-8 p-0"
                      >
                        <RefreshCw className="h-4 w-4" />
                      </Button>
                    )}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={clearOutput}
                      className="h-8 w-8 p-0 hover:bg-destructive/10 hover:text-destructive"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                
                <Separator />
                
                <div className="relative">
                  {/* 生成状态显示 */}
                  {isGenerating && videoResult?.status === "generating" && (
                    <div className="aspect-video bg-muted rounded-lg flex items-center justify-center">
                      <div className="text-center space-y-4">
                        <Loader2 className="h-12 w-12 animate-spin mx-auto text-primary" />
                        <div className="space-y-2">
                          <p className="text-sm text-muted-foreground">{texts.generatingText}</p>
                          <div className="w-64 bg-secondary rounded-full h-2">
                            <div 
                              className="bg-primary h-2 rounded-full transition-all duration-300"
                              style={{ width: `${videoResult.progress || 0}%` }}
                            />
                          </div>
                          <p className="text-xs text-muted-foreground">
                            {Math.round(videoResult.progress || 0)}%
                          </p>
                          {currentTaskId && (
                            <p className="text-xs text-muted-foreground">
                              {texts.taskId}: {currentTaskId.slice(0, 8)}...
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  )}

                  {/* 视频播放器 */}
                  {videoResult?.status === "completed" && videoResult.url && (
                    <div className="aspect-video bg-black rounded-lg overflow-hidden relative group">
                      <video
                        ref={videoRef}
                        src={videoResult.url}
                        className="w-full h-full object-cover"
                        onTimeUpdate={(e) => setCurrentTime((e.target as HTMLVideoElement).currentTime)}
                        onLoadedMetadata={(e) => setDuration((e.target as HTMLVideoElement).duration)}
                        onPlay={() => setIsPlaying(true)}
                        onPause={() => setIsPlaying(false)}
                      />
                      
                      {/* 视频控制层 */}
                      <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity">
                        <div className="absolute inset-0 flex items-center justify-center">
                          <Button
                            variant="ghost"
                            size="lg"
                            onClick={togglePlay}
                            className="bg-black/50 hover:bg-black/70 text-white"
                          >
                            {isPlaying ? (
                              <Pause className="h-8 w-8" />
                            ) : (
                              <Play className="h-8 w-8" />
                            )}
                          </Button>
                        </div>
                        
                        <div className="absolute bottom-4 left-4 right-4 space-y-2">
                          {/* 进度条 */}
                          <div className="w-full bg-white/20 rounded-full h-1">
                            <div 
                              className="bg-white h-1 rounded-full"
                              style={{ width: `${duration ? (currentTime / duration) * 100 : 0}%` }}
                            />
                          </div>
                          
                          {/* 控制按钮 */}
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={toggleMute}
                                className="text-white hover:bg-white/20"
                              >
                                {isMuted ? (
                                  <VolumeX className="h-4 w-4" />
                                ) : (
                                  <Volume2 className="h-4 w-4" />
                                )}
                              </Button>
                              <span className="text-white text-sm">
                                {Math.floor(currentTime)}:{Math.floor(currentTime % 60).toString().padStart(2, '0')} / 
                                {Math.floor(duration)}:{Math.floor(duration % 60).toString().padStart(2, '0')}
                              </span>
                            </div>
                            
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => videoRef.current?.requestFullscreen()}
                              className="text-white hover:bg-white/20"
                            >
                              <Maximize className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* 默认状态 */}
                  {!videoResult && !isGenerating && (
                    <div className="aspect-video border-2 border-dashed border-muted rounded-lg flex items-center justify-center">
                      <div className="text-center text-muted-foreground">
                        <Video className="h-16 w-16 mx-auto mb-4 opacity-20" />
                        <p>{texts.resultPlaceholder}</p>
                      </div>
                    </div>
                  )}

                  {/* 生成失败状态 */}
                  {videoResult?.status === "failed" && (
                    <div className="aspect-video border border-destructive/20 bg-destructive/5 rounded-lg flex items-center justify-center">
                      <div className="text-center space-y-4">
                        <Video className="h-16 w-16 mx-auto opacity-50 text-destructive" />
                        <div>
                          <p className="text-destructive mb-2">{texts.generationFailed}</p>
                          <Button onClick={handleRetry} variant="outline" size="sm">
                            <RefreshCw className="h-4 w-4 mr-2" />
                            {texts.retryButton}
                          </Button>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* 操作按钮 */}
                {videoResult?.status === "completed" && (
                  <div className="flex gap-2">
                    {/* <Button
                      variant="outline"
                      size="sm"
                      onClick={handleCopyLink}
                      className="flex-1"
                    >
                      <Copy className="h-4 w-4 mr-2" />
                      {texts.copyButton}
                    </Button> */}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleDownload}
                      className="flex-1"
                    >
                      <Download className="h-4 w-4 mr-2" />
                      {texts.downloadButton}
                    </Button>
                    {/* <Button
                      variant="outline"
                      size="sm"
                      onClick={handleShare}
                      className="flex-1"
                    >
                      <Share2 className="h-4 w-4 mr-2" />
                      {texts.shareButton}
                    </Button> */}
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
}
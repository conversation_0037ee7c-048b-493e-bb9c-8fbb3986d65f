"use client";

import { Badge } from "@/components/ui/badge";
import { Link } from "@/i18n/routing";
import { useAppContext } from "@/contexts/app";
import { useEffect, useState } from "react";
import { UserCredits } from "@/types/user";
import Icon from "@/components/icon";
import { useTranslations } from "next-intl";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

export default function CreditsDisplay() {
  const t = useTranslations();
  const { user } = useAppContext();
  const [credits, setCredits] = useState<UserCredits | null>(null);
  const [loading, setLoading] = useState(false);

  const fetchCredits = async () => {
    if (!user) return;
    
    setLoading(true);
    try {
      const response = await fetch("/api/get-user-credits", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });
      
      if (response.ok) {
        const { code, data } = await response.json();
        if (code === 0) {
          setCredits(data);
        } else {
          console.error("API returned error code:", code);
        }
      } else {
        console.error("HTTP error:", response.status);
      }
    } catch (error) {
      console.error("Failed to fetch credits:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCredits();
  }, [user]);

  // 如果用户未登录，不显示
  if (!user) {
    return null;
  }

  // 如果正在加载，显示加载状态
  if (loading) {
    return (
      <Badge variant="outline" className="cursor-pointer animate-pulse">
        <Icon name="RiBankCardLine" className="size-3" />
        <span>---</span>
      </Badge>
    );
  }

  // 显示 credits 信息
  const leftCredits = credits?.left_credits ?? 0;

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Link href={"/my-credits" as any}>
            <Badge 
              variant="outline" 
              className="cursor-pointer hover:bg-accent hover:text-accent-foreground transition-colors"
            >
              <Icon name="RiBankCardLine" className="size-3" />
              <span>{leftCredits.toLocaleString()}</span>
            </Badge>
          </Link>
        </TooltipTrigger>
        <TooltipContent>
          <p>{t("my_credits.left_tip", { left_credits: leftCredits })}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
} 
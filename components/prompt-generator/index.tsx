"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Co<PERSON>, Loader2, <PERSON><PERSON><PERSON>, Trash2, Heart, Brain, Zap, AlertCircle } from "lucide-react";
import { toast } from "sonner";
import { useTranslations } from "next-intl";
import { useAppContext } from "@/contexts/app";
import FavoriteModal from "@/components/ui/favorite-modal";
import { PromptCategory } from "@/types/prompt-category";
import { UserCredits } from "@/types/user";
import { Link } from "@/i18n/routing";

export default function PromptGenerator() {
  const {user, setShowSignModal} = useAppContext();
  const t = useTranslations("prompt_generator");
  const [input, setInput] = useState("");
  const [output, setOutput] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [showFavoriteModal, setShowFavoriteModal] = useState(false);
  const [categories, setCategories] = useState<PromptCategory[]>([]);
  const [isLoadingCategories, setIsLoadingCategories] = useState(false);
  const [mode, setMode] = useState<"basic" | "advanced">("basic");
  const [credits, setCredits] = useState<UserCredits | null>(null);
  const [loadingCredits, setLoadingCredits] = useState(false);

  // 积分相关常量
  const PROMPT_GENERATOR_COST = 1;

  // 获取用户积分
  const fetchCredits = async () => {
    if (!user) return;

    setLoadingCredits(true);
    try {
      const response = await fetch("/api/get-user-credits", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (response.ok) {
        const { code, data } = await response.json();
        if (code === 0) {
          setCredits(data);
        } else {
          console.error("API returned error code:", code);
        }
      } else {
        console.error("HTTP error:", response.status);
      }
    } catch (error) {
      console.error("Failed to fetch credits:", error);
    } finally {
      setLoadingCredits(false);
    }
  };

  // 监听用户登录状态，恢复临时保存的内容
  useEffect(() => {
    if (user) {
      // 获取用户积分
      fetchCredits();

      // 检查是否有收藏相关的临时数据
      const tempOutput = localStorage.getItem('temp_prompt_output');
      if (tempOutput) {
        setOutput(tempOutput);
        localStorage.removeItem('temp_prompt_output');
        // 自动打开收藏模态框
        setShowFavoriteModal(true);
      }

      // 检查是否有生成相关的临时数据
      const tempInput = localStorage.getItem('temp_prompt_input');
      const tempMode = localStorage.getItem('temp_prompt_mode');
      const shouldGenerate = localStorage.getItem('temp_should_generate');

      if (tempInput && tempMode && shouldGenerate === 'true') {
        // 恢复输入内容和模式
        setInput(tempInput);
        setMode(tempMode as "basic" | "advanced");

        // 清理临时数据
        localStorage.removeItem('temp_prompt_input');
        localStorage.removeItem('temp_prompt_mode');
        localStorage.removeItem('temp_should_generate');

        // 提示用户可以继续生成
        toast.success(t("login_success_continue"));
      }
    } else {
      // 未登录时默认基础模式
      setMode("basic");
      setCredits(null);
    }
  }, [user]);

  const requestGenText = async (input: string, mode: string, locale: string) => {
    const response = await fetch("/api/generate/gen-text", {
      method: "POST",
      body: JSON.stringify({input, mode, locale}),
    });
    return response.json();
  };

  const handleGenerate = async () => {

    if (!input.trim()) {
      toast.error(t("error_no_input"));
      return;
    }

    // 如果是高级模式且用户未登录，弹出登录框并保存数据
    if (mode === "advanced" && !user) {
      // 保存当前输入内容和模式
      localStorage.setItem('temp_prompt_input', input);
      localStorage.setItem('temp_prompt_mode', mode);
      localStorage.setItem('temp_should_generate', 'true');
      setShowSignModal(true);
      return;
    }

    // 如果是高级模式且积分不足，提示用户
    if (mode === "advanced" && user && credits && credits.left_credits < PROMPT_GENERATOR_COST) {
      toast.error(t("credits_insufficient_hint"));
      return;
    }

    setIsGenerating(true);

    try {
      const locale = document.documentElement.lang || "en";
      console.log(input);
      const response = await requestGenText(input, mode, locale);
      console.log(response);
      if (response.code === -1) {
        toast.error(response.message);
        return;
      }

      setOutput(response.data.text);
      toast.success(t("success_generated"));

      // 如果是高级模式，重新获取积分（因为积分已被消耗）
      if (mode === "advanced" && user) {
        fetchCredits();
      }
    } catch (error) {
      console.error(error);
      toast.error(t("error_generate_failed"));
    } finally {
      setIsGenerating(false);
    }
  };

  const handleCopyOutput = async () => {
    if (!output) {
      toast.error(t("error_no_content"));
      return;
    }

    try {
      await navigator.clipboard.writeText(output);
      toast.success(t("success_copied"));
    } catch (error) {
      toast.error(t("error_copy_failed"));
    }
  };

  const handleClearOutput = () => {
    setOutput("");
  };

  const handleClearInput = () => {
    setInput("");
  };

  const handleModeChange = (newMode: "basic" | "advanced") => {
    setMode(newMode);
  };

  const handleFavorite = async () => {
    console.log('点击收藏按钮，用户状态:', user ? '已登录' : '未登录');
    console.log('当前输出内容:', output);
    
    // 检查用户登录状态
    if (!user) {
      // 保存当前输出内容到 localStorage
      localStorage.setItem('temp_prompt_output', output);
      setShowSignModal(true);
      return;
    }
    
    // 用户已登录，获取分类并打开收藏模态框
    console.log('开始获取分类并打开模态框...');
    await fetchCategories();
    console.log('分类获取完成，当前categories:', categories);
    setShowFavoriteModal(true);
    console.log('模态框已设置为显示');
  };

  const fetchCategories = async () => {
    setIsLoadingCategories(true);
    try {
      const response = await fetch('/api/prompt/categories');
      
      const data = await response.json();
      console.log('分类API原始响应数据:', data);
      
      if (data.code === 0) {
        const categoriesData = data.data.categories || [];
        setCategories(categoriesData);
      } else {
        toast.error(t("error_get_categories_failed_with_msg", { message: data.message || '未知错误' }));
      }
    } catch (error) {
      console.error('获取分类异常:', error);
      toast.error(t("error_get_categories_failed"));
    } finally {
      setIsLoadingCategories(false);
    }
  };

  const createNewCategory = async (name: string) => {
    try {
      const response = await fetch('/api/prompt/categories', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name: name.trim() }),
      });

      const data = await response.json();
      if (data.code === 0) {
        return data.data.uuid;
      } else {
        // 根据错误类型显示不同的提示
        if (data.message.includes("already exists")) {
          throw new Error(`category "${name.trim()}" already exists, please use another name`);
        } else {
          throw new Error(data.message || t("error_favorite_failed"));
        }
      }
    } catch (error) {
      throw error;
    }
  };

  const handleFavoriteSave = async (data: {
    title: string;
    content: string;
    notes: string;
    categoryUuid: string | null;
    isNewCategory: boolean;
    newCategoryName: string;
  }) => {
    try {
      // FavoriteModal 已经处理了分类创建，这里直接使用传入的 categoryUuid
      let categoryUuid = data.categoryUuid;

      const response = await fetch('/api/prompt/favorites', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: data.title,
          prompt: data.content,
          category_uuid: categoryUuid || "",
          notes: data.notes || "",
        }),
      });

      const result = await response.json();
      console.log(result);
      if (result.code === 0) {
        toast.success(t("success_favorite_saved"));
        // 成功后会自动关闭模态框
      } else {
        toast.error(result.message || t("error_favorite_failed"));
        throw new Error(result.message || t("error_favorite_failed"));
      }
    } catch (error) {
      toast.error(error instanceof Error ? error.message : t("error_favorite_failed"));
      throw error;
    }
  };

  return (
    <section id="generator">
      {/* <div className="container"> */}
        <Card className="w-full max-w-7xl mx-auto">
          <CardHeader className="text-center">
            <CardTitle className="flex items-center justify-center gap-2 text-2xl">
              <Sparkles className="h-6 w-6 text-primary" />
              {t("title")}
            </CardTitle>
            <p className="text-muted-foreground">
              {t("subtitle")}
            </p>
          </CardHeader>
          
          <CardContent>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 左侧输入区域 */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="input" className="text-base font-medium">
                    {t("input_label")}
                  </Label>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleClearInput}
                    className="h-8 w-8 p-0 hover:bg-destructive/10 hover:text-destructive"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
                
                <Separator />

                <Textarea
                  id="input"
                  placeholder={t("input_placeholder")}
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  className="min-h-[250px] resize-none"
                />

                {/* 模式选择Toggle */}
                <div className="space-y-4">
                  {/* 分段控制器Toggle */}
                  <div className="space-y-3">
                    <div className="relative bg-gray-100 p-1 rounded-lg">
                      <div className="grid grid-cols-2 relative">
                        {/* 滑动背景 */}
                        <div
                          className={`
                            absolute top-1 bottom-1 w-1/2 bg-white rounded-md shadow-sm transition-transform duration-200 ease-out
                            ${mode === 'advanced' ? 'translate-x-full' : 'translate-x-0'}
                          `}
                        />

                        {/* 基础模式按钮 */}
                        <button
                          onClick={() => handleModeChange('basic')}
                          className={`
                            relative z-10 flex items-center justify-center gap-2 py-2 px-4 text-sm font-medium rounded-md transition-colors duration-200
                            ${mode === 'basic' ? 'text-blue-600' : 'text-gray-600 hover:text-gray-800'}
                          `}
                        >
                          <Zap className="h-4 w-4" />
                          {t("mode_basic")}
                        </button>

                        {/* 高级模式按钮 */}
                        <button
                          onClick={() => handleModeChange('advanced')}
                          className={`
                            relative z-10 flex items-center justify-center gap-2 py-2 px-4 text-sm font-medium rounded-md transition-colors duration-200
                            ${mode === 'advanced' ? 'text-purple-600' : 'text-gray-600 hover:text-gray-800'}
                            ${!user ? 'hover:text-purple-600' : ''}
                          `}
                        >
                          <Brain className="h-4 w-4" />
                          {t("mode_advanced")}
                        </button>
                      </div>
                    </div>

                    {/* 模式提示文案 */}
                    <div className="text-center">
                      {mode === "basic" ? (
                        <p className="text-sm text-gray-600">
                          {t("basic_mode_simple_hint")}
                        </p>
                      ) : (
                        <p className="text-sm text-gray-600">
                          {t("advanced_mode_simple_hint")}
                          {!user && ` • ${t("login_required")}`}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* 积分消耗提示 - 只在高级模式且用户已登录时显示 */}
                  {mode === 'advanced' && user && (
                    <div className="text-center">
                      {loadingCredits ? (
                        <p className="text-sm text-gray-500">
                          <Loader2 className="inline h-3 w-3 animate-spin mr-1" />
                          Loading credits...
                        </p>
                      ) : credits && credits.left_credits >= PROMPT_GENERATOR_COST ? (
                        <p className="text-sm text-gray-600">
                          {t("credits_cost_hint", { left_credits: credits.left_credits })}
                        </p>
                      ) : (
                        <div className="flex items-center justify-center gap-2 text-sm text-red-600">
                          <AlertCircle className="h-4 w-4" />
                          <span>{t("credits_insufficient_hint")}</span>
                          <Link href={"/pricing" as any} className="text-blue-600 hover:underline">
                            {t("credits_recharge")}
                          </Link>
                        </div>
                      )}
                    </div>
                  )}

                  {/* 第二行：生成按钮 */}
                  <Button
                    onClick={handleGenerate}
                    disabled={
                      isGenerating ||
                      !input.trim() ||
                      (mode === 'advanced' && user && credits && credits.left_credits < PROMPT_GENERATOR_COST)
                    }
                    size="lg"
                    className="w-full"
                  >
                    {isGenerating ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        {t("generating")}
                      </>
                    ) : (
                      <>
                        <Sparkles className="mr-2 h-4 w-4" />
                        {t("generate_button")}
                      </>
                    )}
                  </Button>
                </div>
              </div>

              {/* 右侧输出区域 */}
              <div className="space-y-4 flex flex-col h-full">
                <div className="flex items-center justify-between">
                  <Label className="text-base font-medium">
                    {t("output_label")}
                  </Label>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleClearOutput}
                    className="h-8 w-8 p-0 hover:bg-destructive/10 hover:text-destructive"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
                
                <Separator />
                
                <div className="relative flex-1">
                  <Textarea
                    placeholder={t("output_placeholder")}
                    value={output}
                    onChange={(e) => setOutput(e.target.value)}
                    className="min-h-[250px] resize-none h-full"
                  />


                  {isGenerating && (
                    <div className="absolute inset-0 flex items-center justify-center pointer-events-none bg-background/80">
                      <div className="text-center">
                        <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2 text-primary" />
                        <p className="text-sm text-muted-foreground">{t("generating_text")}</p>
                      </div>
                    </div>
                  )}
                </div>

                {/* 操作按钮区域 - 始终显示，与左侧生成按钮对齐 */}
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="lg"
                    onClick={handleCopyOutput}
                    disabled={!output}
                    className="flex-1"
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    {t("copy_button")}
                  </Button>
                  <Button
                    variant="outline"
                    size="lg"
                    onClick={handleFavorite}
                    disabled={!output}
                    className="flex-1"
                  >
                    <Heart className="h-4 w-4 mr-2" />
                    {t("favorite_button")}
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>

          {/* 收藏模态框 */}
          <FavoriteModal
            open={showFavoriteModal}
            onOpenChange={setShowFavoriteModal}
            mode="create"
            initialData={{
              content: output,
            }}
            categories={categories}
            isLoadingCategories={isLoadingCategories}
            onSave={handleFavoriteSave}
            onCreateCategory={createNewCategory}
          />
        </Card>
      {/* </div> */}
    </section>
  );
}

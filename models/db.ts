import { createClient } from "@supabase/supabase-js";

export function getSupabaseClient() {
  const supabaseUrl = process.env.SUPABASE_URL || "";

  let supabaseKey = process.env.SUPABASE_ANON_KEY || "";
  if (process.env.SUPABASE_SERVICE_ROLE_KEY) {
    supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  }

  if (!supabaseUrl || !supabaseKey) {
    throw new Error("Supabase URL or key is not set");
  }

  const client = createClient(supabaseUrl, supabaseKey);

  return client;
}

// 事务相关的类型定义
export interface TransactionResult {
  success: boolean;
  task_id?: number;
  remaining_credits?: number;
  trans_no?: string;
  error?: string;
  message?: string;
}

export interface TaskFailureResult {
  success: boolean;
  refund_trans_no?: string;
  error?: string;
  message?: string;
}



// 事务相关函数
export async function executeVeo3TaskTransaction(params: {
  user_uuid: string;
  task_type: string;
  task_params: any;
  task_cost_credit: number;
  callback_token: string;
  trans_type: string;
}): Promise<TransactionResult> {
  const supabase = getSupabaseClient();
  
  try {
    const { data, error } = await supabase.rpc('handle_veo3_task_transaction', {
      p_user_uuid: params.user_uuid,
      p_task_type: params.task_type,
      p_task_params: JSON.stringify(params.task_params),
      p_task_cost_credit: params.task_cost_credit,
      p_callback_token: params.callback_token,
      p_trans_type: params.trans_type
    });

    if (error) {
      console.error('Transaction RPC error:', error);
      return {
        success: false,
        error: 'rpc_call_failed',
        message: error.message
      };
    }

    return data as TransactionResult;
  } catch (error) {
    console.error('Transaction error:', error);
    return {
      success: false,
      error: 'transaction_exception',
      message: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

export async function handleTaskFailure(params: {
  task_id: number;
  user_uuid: string;
  refund_credits: number;
  refund_trans_type: string;
  error_info?: string;
}): Promise<TaskFailureResult> {
  const supabase = getSupabaseClient();
  
  try {
    const { data, error } = await supabase.rpc('handle_task_failure', {
      p_task_id: params.task_id,
      p_user_uuid: params.user_uuid,
      p_refund_credits: params.refund_credits,
      p_refund_trans_type: params.refund_trans_type,
      p_error_info: params.error_info
    });

    if (error) {
      console.error('Task failure RPC error:', error);
      return {
        success: false,
        error: 'rpc_call_failed',
        message: error.message
      };
    }

    return data as TaskFailureResult;
  } catch (error) {
    console.error('Task failure error:', error);
    return {
      success: false,
      error: 'failure_handling_exception',
      message: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}



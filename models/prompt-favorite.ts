import { 
  UserPromptFavorite, 
  CreatePromptFavoriteRequest, 
  UpdatePromptFavoriteRequest,
  PromptFavoriteWithCategory 
} from "@/types/prompt-favorite";
import { getIsoTimestr } from "@/lib/time";
import { getSupabaseClient } from "./db";
import { v4 as uuidv4 } from "uuid";

export async function createPromptFavorite(
  user_uuid: string,
  request: CreatePromptFavoriteRequest
): Promise<UserPromptFavorite> {
  const supabase = getSupabaseClient();
  
  // 如果指定了分类，验证分类是否属于该用户
  if (request.category_uuid) {
    const { data: category } = await supabase
      .from("prompt_categories")
      .select("uuid")
      .eq("uuid", request.category_uuid)
      .eq("user_uuid", user_uuid)
      .single();
    
    if (!category) {
      throw new Error("分类不存在或无权限访问");
    }
  }

  const newFavorite: Omit<UserPromptFavorite, 'id'> = {
    uuid: uuidv4(),
    user_uuid,
    category_uuid: request.category_uuid || undefined,
    prompt: request.prompt,
    model_name: request.model_name || undefined,
    title: request.title || undefined,
    notes: request.notes || undefined,
    created_at: getIsoTimestr(),
    updated_at: getIsoTimestr(),
  };

  const { data, error } = await supabase
    .from("user_prompt_favorites")
    .insert(newFavorite)
    .select()
    .single();

  if (error) {
    throw error;
  }

  return data;
}

export async function getUserPromptFavorites(
  user_uuid: string,
  category_uuid?: string,
  page: number = 1,
  limit: number = 20
): Promise<PromptFavoriteWithCategory[]> {
  if (page < 1) page = 1;
  if (limit <= 0) limit = 20;

  const offset = (page - 1) * limit;
  const supabase = getSupabaseClient();

  // 1. 先查询收藏数据
  let query = supabase
    .from("user_prompt_favorites")
    .select("*")
    .eq("user_uuid", user_uuid);

  // 如果指定了分类，则筛选特定分类
  if (category_uuid) {
    query = query.eq("category_uuid", category_uuid);
  }

  const { data: favorites, error } = await query
    .order("created_at", { ascending: false })
    .range(offset, offset + limit - 1);

  if (error || !favorites) {
    return [];
  }

  // 2. 获取所有相关的分类信息
  const categoryUuids = favorites
    .map(f => f.category_uuid)
    .filter((uuid): uuid is string => uuid !== null && uuid !== undefined);

  let categories: { [key: string]: string } = {};
  
  if (categoryUuids.length > 0) {
    const { data: categoriesData } = await supabase
      .from("prompt_categories")
      .select("uuid, name")
      .in("uuid", categoryUuids);

    if (categoriesData) {
      categories = categoriesData.reduce((acc, cat) => {
        acc[cat.uuid] = cat.name;
        return acc;
      }, {} as { [key: string]: string });
    }
  }

  // 3. 在应用层关联数据
  return favorites.map(favorite => ({
    ...favorite,
    category_name: favorite.category_uuid ? categories[favorite.category_uuid] || null : null
  }));
}

export async function findPromptFavoriteByUuid(
  uuid: string,
  user_uuid: string
): Promise<PromptFavoriteWithCategory | undefined> {
  const supabase = getSupabaseClient();
  
  // 1. 查询收藏数据
  const { data: favorite, error } = await supabase
    .from("user_prompt_favorites")
    .select("*")
    .eq("uuid", uuid)
    .eq("user_uuid", user_uuid) // 用户权限控制
    .single();

  if (error || !favorite) {
    return undefined;
  }

  // 2. 如果有分类，查询分类名称
  let category_name = null;
  if (favorite.category_uuid) {
    const { data: category } = await supabase
      .from("prompt_categories")
      .select("name")
      .eq("uuid", favorite.category_uuid)
      .single();
    
    category_name = category?.name || null;
  }

  return {
    ...favorite,
    category_name
  };
}

export async function updatePromptFavorite(
  uuid: string,
  user_uuid: string,
  request: UpdatePromptFavoriteRequest
): Promise<UserPromptFavorite | undefined> {
  const supabase = getSupabaseClient();
  
  // 如果要更新分类，验证分类是否属于该用户
  if (request.category_uuid) {
    const { data: category } = await supabase
      .from("prompt_categories")
      .select("uuid")
      .eq("uuid", request.category_uuid)
      .eq("user_uuid", user_uuid)
      .single();
    
    if (!category) {
      throw new Error("分类不存在或无权限访问");
    }
  }
  
  const updateData = {
    ...request,
    updated_at: getIsoTimestr(),
  };

  const { data, error } = await supabase
    .from("user_prompt_favorites")
    .update(updateData)
    .eq("uuid", uuid)
    .eq("user_uuid", user_uuid) // 用户权限控制
    .select()
    .single();

  if (error) {
    throw error;
  }

  return data;
}

export async function deletePromptFavorite(
  uuid: string,
  user_uuid: string
): Promise<boolean> {
  const supabase = getSupabaseClient();
  
  const { error } = await supabase
    .from("user_prompt_favorites")
    .delete()
    .eq("uuid", uuid)
    .eq("user_uuid", user_uuid); // 用户权限控制

  if (error) {
    throw error;
  }

  return true;
}

export async function getUserPromptFavoritesTotal(
  user_uuid: string,
  category_uuid?: string
): Promise<number> {
  const supabase = getSupabaseClient();
  
  let query = supabase
    .from("user_prompt_favorites")
    .select("count", { count: "exact" })
    .eq("user_uuid", user_uuid);

  if (category_uuid) {
    query = query.eq("category_uuid", category_uuid);
  }

  const { data, error } = await query;

  if (error) {
    return 0;
  }

  return data[0]?.count || 0;
}

export async function searchUserPromptFavorites(
  user_uuid: string,
  keyword: string,
  page: number = 1,
  limit: number = 20
): Promise<PromptFavoriteWithCategory[]> {
  if (page < 1) page = 1;
  if (limit <= 0) limit = 20;

  const offset = (page - 1) * limit;
  const supabase = getSupabaseClient();

  // 1. 搜索收藏数据
  const { data: favorites, error } = await supabase
    .from("user_prompt_favorites")
    .select("*")
    .eq("user_uuid", user_uuid)
    .or(`prompt.ilike.%${keyword}%,title.ilike.%${keyword}%,notes.ilike.%${keyword}%`)
    .order("created_at", { ascending: false })
    .range(offset, offset + limit - 1);

  if (error || !favorites) {
    return [];
  }

  // 2. 获取所有相关的分类信息
  const categoryUuids = favorites
    .map(f => f.category_uuid)
    .filter((uuid): uuid is string => uuid !== null && uuid !== undefined);

  let categories: { [key: string]: string } = {};
  
  if (categoryUuids.length > 0) {
    const { data: categoriesData } = await supabase
      .from("prompt_categories")
      .select("uuid, name")
      .in("uuid", categoryUuids);

    if (categoriesData) {
      categories = categoriesData.reduce((acc, cat) => {
        acc[cat.uuid] = cat.name;
        return acc;
      }, {} as { [key: string]: string });
    }
  }

  // 3. 在应用层关联数据
  return favorites.map(favorite => ({
    ...favorite,
    category_name: favorite.category_uuid ? categories[favorite.category_uuid] || null : null
  }));
}

export async function movePromptFavoriteToCategory(
  uuid: string,
  user_uuid: string,
  category_uuid: string | null
): Promise<UserPromptFavorite | undefined> {
  const supabase = getSupabaseClient();
  
  // 如果指定了分类，验证分类是否属于该用户
  if (category_uuid) {
    const { data: category } = await supabase
      .from("prompt_categories")
      .select("uuid")
      .eq("uuid", category_uuid)
      .eq("user_uuid", user_uuid)
      .single();
    
    if (!category) {
      throw new Error("分类不存在或无权限访问");
    }
  }

  const { data, error } = await supabase
    .from("user_prompt_favorites")
    .update({ 
      category_uuid,
      updated_at: getIsoTimestr() 
    })
    .eq("uuid", uuid)
    .eq("user_uuid", user_uuid) // 用户权限控制
    .select()
    .single();

  if (error) {
    throw error;
  }

  return data;
} 
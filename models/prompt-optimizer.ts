import { 
  PromptOptimizer, 
  CreatePromptOptimizerRequest, 
  UpdatePromptOptimizerRequest,
  PromptOptimizerFilters 
} from "@/types/prompt-optimizer";
import { getIsoTimestr } from "@/lib/time";
import { getSupabaseClient } from "./db";

export async function createPromptOptimizer(
  request: CreatePromptOptimizerRequest
): Promise<PromptOptimizer> {
  const supabase = getSupabaseClient();
  
  const newOptimizer: Omit<PromptOptimizer, 'id'> = {
    prompt: request.prompt || undefined,
    model_type: request.model_type,
    model_name: request.model_name,
    language: request.language || 'en',
    version: request.version || '1.0.0',
    status: request.status || 'offline',
    created_at: getIsoTimestr(),
    updated_at: getIsoTimestr(),
  };

  const { data, error } = await supabase
    .from("prompt_optimizers")
    .insert(newOptimizer)
    .select()
    .single();

  if (error) {
    throw error;
  }

  return data;
}

export async function getPromptOptimizers(
  filters?: PromptOptimizerFilters,
  page: number = 1,
  limit: number = 20
): Promise<PromptOptimizer[]> {
  if (page < 1) page = 1;
  if (limit <= 0) limit = 20;

  const offset = (page - 1) * limit;
  const supabase = getSupabaseClient();

  let query = supabase
    .from("prompt_optimizers")
    .select("*");

  // 应用筛选条件
  if (filters?.model_type) {
    query = query.eq("model_type", filters.model_type);
  }
  if (filters?.model_name) {
    query = query.eq("model_name", filters.model_name);
  }
  if (filters?.language) {
    query = query.eq("language", filters.language);
  }
  if (filters?.status) {
    query = query.eq("status", filters.status);
  }
  if (filters?.version) {
    query = query.eq("version", filters.version);
  }

  const { data, error } = await query
    .order("created_at", { ascending: false })
    .range(offset, offset + limit - 1);

  if (error) {
    throw error;
  }

  return data || [];
}

export async function findPromptOptimizerById(
  id: number
): Promise<PromptOptimizer | undefined> {
  const supabase = getSupabaseClient();
  
  const { data, error } = await supabase
    .from("prompt_optimizers")
    .select("*")
    .eq("id", id)
    .single();

  if (error || !data) {
    return undefined;
  }

  return data;
}

export async function updatePromptOptimizer(
  id: number,
  request: UpdatePromptOptimizerRequest
): Promise<PromptOptimizer | undefined> {
  const supabase = getSupabaseClient();
  
  const updateData = {
    ...request,
    updated_at: getIsoTimestr(),
  };

  const { data, error } = await supabase
    .from("prompt_optimizers")
    .update(updateData)
    .eq("id", id)
    .select()
    .single();

  if (error) {
    throw error;
  }

  return data;
}

export async function deletePromptOptimizer(
  id: number
): Promise<boolean> {
  const supabase = getSupabaseClient();
  
  const { error } = await supabase
    .from("prompt_optimizers")
    .delete()
    .eq("id", id);

  if (error) {
    throw error;
  }

  return true;
}

export async function getPromptOptimizersTotal(
  filters?: PromptOptimizerFilters
): Promise<number> {
  const supabase = getSupabaseClient();
  
  let query = supabase
    .from("prompt_optimizers")
    .select("count", { count: "exact" });

  // 应用筛选条件
  if (filters?.model_type) {
    query = query.eq("model_type", filters.model_type);
  }
  if (filters?.model_name) {
    query = query.eq("model_name", filters.model_name);
  }
  if (filters?.language) {
    query = query.eq("language", filters.language);
  }
  if (filters?.status) {
    query = query.eq("status", filters.status);
  }
  if (filters?.version) {
    query = query.eq("version", filters.version);
  }

  const { data, error } = await query;

  if (error) {
    return 0;
  }

  return data[0]?.count || 0;
}

export async function searchPromptOptimizers(
  keyword: string,
  filters?: PromptOptimizerFilters,
  page: number = 1,
  limit: number = 20
): Promise<PromptOptimizer[]> {
  if (page < 1) page = 1;
  if (limit <= 0) limit = 20;

  const offset = (page - 1) * limit;
  const supabase = getSupabaseClient();

  let query = supabase
    .from("prompt_optimizers")
    .select("*")
    .or(`prompt.ilike.%${keyword}%,model_name.ilike.%${keyword}%`);

  // 应用筛选条件
  if (filters?.model_type) {
    query = query.eq("model_type", filters.model_type);
  }
  if (filters?.model_name) {
    query = query.eq("model_name", filters.model_name);
  }
  if (filters?.language) {
    query = query.eq("language", filters.language);
  }
  if (filters?.status) {
    query = query.eq("status", filters.status);
  }
  if (filters?.version) {
    query = query.eq("version", filters.version);
  }

  const { data, error } = await query
    .order("created_at", { ascending: false })
    .range(offset, offset + limit - 1);

  if (error) {
    throw error;
  }

  return data || [];
}

export async function getOnlinePromptOptimizers(
  model_type?: 'text' | 'image' | 'video',
  model_name?: string,
  language?: string
): Promise<PromptOptimizer[]> {
  const supabase = getSupabaseClient();

  let query = supabase
    .from("prompt_optimizers")
    .select("*")
    .eq("status", "online");

  if (model_type) {
    query = query.eq("model_type", model_type);
  }
  if (model_name) {
    query = query.eq("model_name", model_name);
  }
  if (language) {
    query = query.eq("language", language);
  }

  const { data, error } = await query
    .order("created_at", { ascending: false });

  if (error) {
    throw error;
  }

  return data || [];
}

export async function updatePromptOptimizerStatus(
  id: number,
  status: 'offline' | 'online'
): Promise<PromptOptimizer | undefined> {
  const supabase = getSupabaseClient();
  
  const { data, error } = await supabase
    .from("prompt_optimizers")
    .update({ 
      status,
      updated_at: getIsoTimestr() 
    })
    .eq("id", id)
    .select()
    .single();

  if (error) {
    throw error;
  }

  return data;
} 
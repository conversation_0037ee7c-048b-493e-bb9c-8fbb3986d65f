import { PromptCategory, CreatePromptCategoryRequest, UpdatePromptCategoryRequest } from "@/types/prompt-category";
import { getIsoTimestr } from "@/lib/time";
import { getSupabaseClient } from "./db";
import { v4 as uuidv4 } from "uuid";

export async function createPromptCategory(
  user_uuid: string,
  request: CreatePromptCategoryRequest
): Promise<PromptCategory> {
  const supabase = getSupabaseClient();
  
  // 先检查是否已存在相同名称的分类
  const { data: existing, error: checkError } = await supabase
    .from('prompt_categories')
    .select('id')
    .eq('user_uuid', user_uuid)
    .eq('name', request.name.trim());

  console.log("checkError", checkError);
  console.log("existing", existing);
  
  // 如果查询出错，抛出实际的查询错误
  if (checkError) {
    throw checkError;
  }

  // 如果查询成功且存在相同名称的分类，抛出已存在错误
  if (existing && existing.length > 0) {
    throw new Error(`category name "${request.name}" already exists`);
  }

  // 如果不存在，则创建新分类
  const newCategory: Omit<PromptCategory, 'id'> = {
    uuid: uuidv4(),
    user_uuid,
    name: request.name.trim(),
    created_at: getIsoTimestr(),
    updated_at: getIsoTimestr(),
  };

  const { data, error } = await supabase
    .from("prompt_categories")
    .insert(newCategory)
    .select()
    .single();

  if (error) {
    throw error;
  }

  return data;
}

export async function getUserPromptCategories(
  user_uuid: string,
  page: number = 1,
  limit: number = 50
): Promise<PromptCategory[]> {
  if (page < 1) page = 1;
  if (limit <= 0) limit = 50;

  const offset = (page - 1) * limit;
  const supabase = getSupabaseClient();

  const { data, error } = await supabase
    .from("prompt_categories")
    .select("*")
    .eq("user_uuid", user_uuid)
    .order("created_at", { ascending: false })
    .range(offset, offset + limit - 1);

  if (error) {
    return [];
  }

  return data;
}

export async function findPromptCategoryByUuid(
  uuid: string,
  user_uuid: string
): Promise<PromptCategory | undefined> {
  const supabase = getSupabaseClient();
  
  const { data, error } = await supabase
    .from("prompt_categories")
    .select("*")
    .eq("uuid", uuid)
    .eq("user_uuid", user_uuid) // 用户权限控制
    .single();

  if (error) {
    return undefined;
  }

  return data;
}

export async function updatePromptCategory(
  uuid: string,
  user_uuid: string,
  request: UpdatePromptCategoryRequest
): Promise<PromptCategory | undefined> {
  const supabase = getSupabaseClient();
  
  const updateData = {
    ...request,
    updated_at: getIsoTimestr(),
  };

  const { data, error } = await supabase
    .from("prompt_categories")
    .update(updateData)
    .eq("uuid", uuid)
    .eq("user_uuid", user_uuid) // 用户权限控制
    .select()
    .single();

  if (error) {
    throw error;
  }

  return data;
}

export async function deletePromptCategory(
  uuid: string,
  user_uuid: string
): Promise<boolean> {
  const supabase = getSupabaseClient();
  
  // 先检查是否还有收藏使用该分类
  const { data: favorites } = await supabase
    .from("user_prompt_favorites")
    .select("id")
    .eq("category_uuid", uuid)
    .eq("user_uuid", user_uuid)
    .limit(1);

  if (favorites && favorites.length > 0) {
    throw new Error("无法删除分类：该分类下还有收藏的提示词");
  }

  const { error } = await supabase
    .from("prompt_categories")
    .delete()
    .eq("uuid", uuid)
    .eq("user_uuid", user_uuid); // 用户权限控制

  if (error) {
    throw error;
  }

  return true;
}

export async function getUserPromptCategoriesTotal(user_uuid: string): Promise<number> {
  const supabase = getSupabaseClient();
  
  const { data, error } = await supabase
    .from("prompt_categories")
    .select("count", { count: "exact" })
    .eq("user_uuid", user_uuid);

  if (error) {
    return 0;
  }

  return data[0]?.count || 0;
} 
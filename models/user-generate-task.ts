import { 
  UserGenerateTask, 
  CreateUserGenerateTaskRequest, 
  UpdateUserGenerateTaskRequest,
  UserGenerateTaskFilters,
  TaskStatus 
} from "@/types/user-generate-task";
import { getIsoTimestr } from "@/lib/time";
import { getSupabaseClient } from "./db";
import { now } from "moment";

/**
 * 生成回调验证token
 * 格式：用户UUID最后8位 + 16位随机字符串
 */
export function generateCallbackToken(user_uuid: string): string {
  // 取用户UUID的最后8位（去掉连字符）
  const cleanUuid = user_uuid.replace(/-/g, '');
  const userSuffix = cleanUuid.slice(-8);
  // 生成16位随机字符串
  const randomPart = crypto.randomUUID().replace(/-/g, '').substring(0, 16);
  
  return `${userSuffix}-${randomPart}`;
}

/**
 * 验证回调token是否属于指定用户
 */
export function validateCallbackToken(token: string, user_uuid: string): boolean {
  if (!token || !user_uuid) return false;
  
  const cleanUuid = user_uuid.replace(/-/g, '');
  const userSuffix = cleanUuid.slice(-8);
  
  return token.startsWith(userSuffix + '-');
}

/**
 * 根据callback_token查找任务
 */
export async function findUserGenerateTaskByToken(
  callback_token: string
): Promise<UserGenerateTask | undefined> {
  const supabase = getSupabaseClient();
  
  const { data, error } = await supabase
    .from("user_generate_tasks")
    .select("*")
    .eq("callback_token", callback_token)
    .single();

  if (error || !data) {
    return undefined;
  }

  return data;
}

/**
 * 创建用户生成任务
 */
export async function createUserGenerateTask(
  request: CreateUserGenerateTaskRequest
): Promise<UserGenerateTask> {
  const supabase = getSupabaseClient();
  
  const newTask: Omit<UserGenerateTask, 'id'> = {
    user_uuid: request.user_uuid,
    task_type: request.task_type,
    task_status: TaskStatus.PROCESSING,
    task_id: request.task_id,
    task_params: request.task_params,
    task_cost_credit: request.task_cost_credit,
    callback_token: request.callback_token,
    version: 1, // 初始版本号
    started_at: getIsoTimestr(),
    created_at: getIsoTimestr(),
    updated_at: getIsoTimestr(),
  };

  const { data, error } = await supabase
    .from("user_generate_tasks")
    .insert(newTask)
    .select()
    .single();

  if (error) {
    throw error;
  }

  return data;
}

/**
 * 根据ID查找任务
 */
export async function findUserGenerateTaskById(
  id: number
): Promise<UserGenerateTask | undefined> {
  const supabase = getSupabaseClient();
  
  const { data, error } = await supabase
    .from("user_generate_tasks")
    .select("*")
    .eq("id", id)
    .single();

  if (error || !data) {
    return undefined;
  }

  return data;
}

/**
 * 根据task_id查找任务
 */
export async function findUserGenerateTaskByTaskId(
  task_id: string
): Promise<UserGenerateTask | undefined> {
  const supabase = getSupabaseClient();
  
  const { data, error } = await supabase
    .from("user_generate_tasks")
    .select("*")
    .eq("task_id", task_id)
    .single();

  if (error || !data) {
    return undefined;
  }

  return data;
}

/**
 * 更新用户生成任务
 */
export async function updateUserGenerateTask(
  id: number,
  request: UpdateUserGenerateTaskRequest
): Promise<UserGenerateTask | undefined> {
  const supabase = getSupabaseClient();
  
  const updateData = {
    ...request,
    updated_at: getIsoTimestr(),
  };

  const { data, error } = await supabase
    .from("user_generate_tasks")
    .update(updateData)
    .eq("id", id)
    .select()
    .single();

  if (error) {
    throw error;
  }

  return data;
}

/**
 * 根据task_id更新任务
 */
export async function updateUserGenerateTaskByTaskId(
  task_id: string,
  user_uuid: string,
  request: UpdateUserGenerateTaskRequest
): Promise<UserGenerateTask | undefined> {
  const supabase = getSupabaseClient();
  
  const updateData = {
    ...request,
    updated_at: getIsoTimestr(),
  };

  const { data, error } = await supabase
    .from("user_generate_tasks")
    .update(updateData)
    .eq("task_id", task_id)
    .eq("user_uuid", user_uuid)
    .select()
    .single();

  if (error) {
    throw error;
  }

  return data;
}

/**
 * 更新任务状态
 */
export async function updateUserGenerateTaskStatus(
  task_id: string,
  task_status: TaskStatus,
  task_result?: string
): Promise<UserGenerateTask | undefined> {
  const supabase = getSupabaseClient();
  
  const updateData: UpdateUserGenerateTaskRequest = {
    task_status,
    updated_at: getIsoTimestr(),
  };

  if (task_result) {
    updateData.task_result = task_result;
  }

  if (task_status === TaskStatus.PROCESSING && !updateData.started_at) {
    updateData.started_at = getIsoTimestr();
  }

  if (task_status === TaskStatus.COMPLETED || task_status === TaskStatus.FAILED) {
    updateData.completed_at = getIsoTimestr();
  }

  const { data, error } = await supabase
    .from("user_generate_tasks")
    .update(updateData)
    .eq("task_id", task_id)
    .select()
    .single();

  if (error) {
    throw error;
  }

  return data;
}

/**
 * 幂等性更新任务状态 - 基于版本控制
 * 只允许从 PROCESSING 状态转换到终态（COMPLETED 或 FAILED）
 * 如果任务已经是终态，则跳过更新
 */
export async function updateTaskStatusIdempotent(
  task_id: string,
  user_uuid: string,
  new_status: TaskStatus,
  task_result?: string,
  expected_version?: number
): Promise<{
  success: boolean;
  task?: UserGenerateTask;
  reason?: string;
}> {
  const supabase = getSupabaseClient();

  try {
    // 首先查询当前任务状态
    const currentTask = await findUserGenerateTaskByTaskId(task_id);
    if (!currentTask) {
      return { success: false, reason: 'Task not found' };
    }

    // 验证用户权限
    if (currentTask.user_uuid !== user_uuid) {
      return { success: false, reason: 'Unauthorized' };
    }

    // 如果任务已经是终态，直接返回成功（幂等性）
    if (currentTask.task_status === TaskStatus.COMPLETED || currentTask.task_status === TaskStatus.FAILED) {
      return { 
        success: true, 
        task: currentTask, 
        reason: 'Task already in final state' 
      };
    }

    // 只允许从 PROCESSING 状态转换到终态
    if (currentTask.task_status !== TaskStatus.PROCESSING) {
      return { 
        success: false, 
        reason: `Invalid state transition from ${currentTask.task_status} to ${new_status}` 
      };
    }

    // 只允许转换到终态
    if (new_status !== TaskStatus.COMPLETED && new_status !== TaskStatus.FAILED) {
      return { 
        success: false, 
        reason: `Invalid target status: ${new_status}` 
      };
    }

    // 准备更新数据
    const updateData: any = {
      task_status: new_status,
      completed_at: getIsoTimestr(),
      updated_at: getIsoTimestr(),
      version: currentTask.version + 1, // 增加版本号
    };

    if (task_result) {
      updateData.task_result = task_result;
    }

    // 构建查询条件：task_id + user_uuid + 当前状态 + 版本号
    let query = supabase
      .from("user_generate_tasks")
      .update(updateData)
      .eq("task_id", task_id)
      .eq("user_uuid", user_uuid)
      .eq("task_status", TaskStatus.PROCESSING); // 确保只能从 PROCESSING 状态更新

    // 如果指定了期望版本，则添加版本检查
    if (expected_version !== undefined) {
      query = query.eq("version", expected_version);
    } else {
      // 使用当前版本进行乐观锁
      query = query.eq("version", currentTask.version);
    }

    const { data, error, count } = await query.select().single();

    if (error) {
      console.error("Update task status error:", error);
      return { success: false, reason: error.message };
    }

    if (!data) {
      // 没有更新任何行，可能是版本冲突或状态已变更
      const latestTask = await findUserGenerateTaskByTaskId(task_id);
      if (latestTask && (latestTask.task_status === TaskStatus.COMPLETED || latestTask.task_status === TaskStatus.FAILED)) {
        // 任务已经完成，返回成功（幂等性）
        return { 
          success: true, 
          task: latestTask, 
          reason: 'Task completed by another request' 
        };
      }
      return { 
        success: false, 
        reason: 'Version conflict or concurrent modification' 
      };
    }

    return { success: true, task: data };
  } catch (error) {
    console.error("updateTaskStatusIdempotent error:", error);
    return { 
      success: false, 
      reason: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * 幂等性更新任务结果 - 用于轮询接口
 * 如果任务状态变化，则更新；如果已经是最终状态，则返回当前状态
 */
export async function updateTaskResultIdempotent(
  task_id: string,
  user_uuid: string,
  new_status: TaskStatus,
  task_result?: string
): Promise<{
  success: boolean;
  task?: UserGenerateTask;
  reason?: string;
  updated: boolean; // 是否实际更新了数据库
}> {
  const supabase = getSupabaseClient();

  try {
    // 查询当前任务
    const currentTask = await findUserGenerateTaskByTaskId(task_id);
    if (!currentTask) {
      return { success: false, reason: 'Task not found', updated: false };
    }

    // 验证用户权限
    if (currentTask.user_uuid !== user_uuid) {
      return { success: false, reason: 'Unauthorized', updated: false };
    }

    // 如果状态没有变化，直接返回（幂等性）
    if (currentTask.task_status === new_status) {
      return { 
        success: true, 
        task: currentTask, 
        reason: 'No status change needed',
        updated: false
      };
    }

    // 如果当前任务已经是终态，不允许更新
    if (currentTask.task_status === TaskStatus.COMPLETED || currentTask.task_status === TaskStatus.FAILED) {
      return { 
        success: true, 
        task: currentTask, 
        reason: 'Task already in final state',
        updated: false
      };
    }

    // 准备更新数据
    const updateData: any = {
      task_status: new_status,
      updated_at: getIsoTimestr(),
      version: currentTask.version + 1,
    };

    if (task_result) {
      updateData.task_result = task_result;
    }

    if (new_status === TaskStatus.COMPLETED || new_status === TaskStatus.FAILED) {
      updateData.completed_at = getIsoTimestr();
    }

    // 使用乐观锁更新
    const { data, error } = await supabase
      .from("user_generate_tasks")
      .update(updateData)
      .eq("task_id", task_id)
      .eq("user_uuid", user_uuid)
      .eq("version", currentTask.version) // 乐观锁
      .select()
      .single();

    if (error) {
      console.error("Update task result error:", error);
      return { success: false, reason: error.message, updated: false };
    }

    if (!data) {
      // 版本冲突，重新获取最新状态
      const latestTask = await findUserGenerateTaskByTaskId(task_id);
      if (latestTask && latestTask.task_status === new_status) {
        // 状态已经是目标状态，返回成功（幂等性）
        return { 
          success: true, 
          task: latestTask, 
          reason: 'Status updated by another request',
          updated: false
        };
      }
      return { 
        success: false, 
        reason: 'Version conflict',
        updated: false
      };
    }

    return { success: true, task: data, updated: true };
  } catch (error) {
    console.error("updateTaskResultIdempotent error:", error);
    return { 
      success: false, 
      reason: error instanceof Error ? error.message : 'Unknown error',
      updated: false
    };
  }
}

/**
 * 获取用户生成任务列表
 */
export async function getUserGenerateTasks(
  filters?: UserGenerateTaskFilters,
  page: number = 1,
  limit: number = 20
): Promise<UserGenerateTask[]> {
  if (page < 1) page = 1;
  if (limit <= 0) limit = 20;

  const offset = (page - 1) * limit;
  const supabase = getSupabaseClient();

  let query = supabase
    .from("user_generate_tasks")
    .select("*");

  // 应用筛选条件
  if (filters?.user_uuid) {
    query = query.eq("user_uuid", filters.user_uuid);
  }
  if (filters?.task_type) {
    query = query.eq("task_type", filters.task_type);
  }
  if (filters?.task_status) {
    query = query.eq("task_status", filters.task_status);
  }
  if (filters?.task_id) {
    query = query.eq("task_id", filters.task_id);
  }

  const { data, error } = await query
    .order("created_at", { ascending: false })
    .range(offset, offset + limit - 1);

  if (error) {
    throw error;
  }

  return data || [];
}

/**
 * 获取用户特定类型的任务
 */
export async function getUserGenerateTasksByType(
  user_uuid: string,
  task_type: string,
  page: number = 1,
  limit: number = 20
): Promise<UserGenerateTask[]> {
  return getUserGenerateTasks(
    { user_uuid, task_type },
    page,
    limit
  );
}

/**
 * 获取用户未完成的任务
 */
export async function getUserPendingTasks(
  user_uuid: string,
  page: number = 1,
  limit: number = 20
): Promise<UserGenerateTask[]> {
  return getUserGenerateTasks(
    { user_uuid, task_status: TaskStatus.PENDING },
    page,
    limit
  );
}

/**
 * 获取任务总数
 */
export async function getUserGenerateTasksTotal(
  filters?: UserGenerateTaskFilters
): Promise<number> {
  const supabase = getSupabaseClient();
  
  let query = supabase
    .from("user_generate_tasks")
    .select("count", { count: "exact" });

  // 应用筛选条件
  if (filters?.user_uuid) {
    query = query.eq("user_uuid", filters.user_uuid);
  }
  if (filters?.task_type) {
    query = query.eq("task_type", filters.task_type);
  }
  if (filters?.task_status) {
    query = query.eq("task_status", filters.task_status);
  }
  if (filters?.task_id) {
    query = query.eq("task_id", filters.task_id);
  }

  const { data, error } = await query;

  if (error) {
    return 0;
  }

  return data[0]?.count || 0;
}

/**
 * 删除用户生成任务
 */
export async function deleteUserGenerateTask(
  id: number
): Promise<boolean> {
  const supabase = getSupabaseClient();
  
  const { error } = await supabase
    .from("user_generate_tasks")
    .delete()
    .eq("id", id);

  if (error) {
    throw error;
  }

  return true;
}

/**
 * 获取用户今日任务统计
 */
export async function getUserTodayTaskStats(
  user_uuid: string
): Promise<{
  total: number;
  pending: number;
  processing: number;
  completed: number;
  failed: number;
}> {
  const supabase = getSupabaseClient();
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const todayStr = today.toISOString();

  const { data, error } = await supabase
    .from("user_generate_tasks")
    .select("task_status")
    .eq("user_uuid", user_uuid)
    .gte("created_at", todayStr);

  if (error) {
    return {
      total: 0,
      pending: 0,
      processing: 0,
      completed: 0,
      failed: 0,
    };
  }

  const stats = {
    total: data.length,
    pending: 0,
    processing: 0,
    completed: 0,
    failed: 0,
  };

  data.forEach(task => {
    switch (task.task_status) {
      case TaskStatus.PENDING:
        stats.pending++;
        break;
      case TaskStatus.PROCESSING:
        stats.processing++;
        break;
      case TaskStatus.COMPLETED:
        stats.completed++;
        break;
      case TaskStatus.FAILED:
        stats.failed++;
        break;
    }
  });

  return stats;
}

/**
 * 获取用户任务成本统计
 */
export async function getUserTaskCostStats(
  user_uuid: string,
  startDate?: string,
  endDate?: string
): Promise<{
  totalCost: number;
  taskCount: number;
  avgCost: number;
}> {
  const supabase = getSupabaseClient();
  
  let query = supabase
    .from("user_generate_tasks")
    .select("task_cost_credit")
    .eq("user_uuid", user_uuid);

  if (startDate) {
    query = query.gte("created_at", startDate);
  }
  if (endDate) {
    query = query.lte("created_at", endDate);
  }

  const { data, error } = await query;

  if (error || !data) {
    return {
      totalCost: 0,
      taskCount: 0,
      avgCost: 0,
    };
  }

  const totalCost = data.reduce((sum, task) => sum + task.task_cost_credit, 0);
  const taskCount = data.length;
  const avgCost = taskCount > 0 ? totalCost / taskCount : 0;

  return {
    totalCost,
    taskCount,
    avgCost,
  };
}

/**
 * 更新任务的外部ID
 */
export async function updateTaskExternalId(
  id: number,
  user_uuid: string,
  external_task_id: string
): Promise<boolean> {
  const supabase = getSupabaseClient();
  
  try {
    const { error } = await supabase
      .from("user_generate_tasks")
      .update({ 
        task_id: external_task_id,
        updated_at: getIsoTimestr()
      })
      .eq("id", id)
      .eq("user_uuid", user_uuid)
      .eq("task_status", TaskStatus.PROCESSING);

    if (error) {
      console.error("Failed to update external task ID:", error);
      return false;
    }

    return true;
  } catch (error) {
    console.error("Exception updating external task ID:", error);
    return false;
  }
} 